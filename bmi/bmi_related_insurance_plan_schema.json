{"table": {"name": "bmi_related_insurance_plan", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_bmi_related_insurance_plan"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "bmi_id", "type": "text", "required": true, "ui_config": {"label": "BMI Static Content", "widget": "relationship_select", "display_field": "title"}, "foreign_key": {"column": "id", "on_delete": "CASCADE", "hasura_table": "site_bmi_static_content"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!"}, {"name": "points", "type": "array", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "grid_cols": 2, "placeholder": "Enter points"}, "graphql_type": "[String!]!"}]}