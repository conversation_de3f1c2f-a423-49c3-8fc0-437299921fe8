{"table": {"name": "bmi_for_men_women_children", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_bmi_for_men_women_children"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "bmi_id", "type": "text", "required": true, "ui_config": {"label": "BMI Static Content", "widget": "relationship_select", "display_field": "title"}, "foreign_key": {"column": "id", "on_delete": "CASCADE", "hasura_table": "site_bmi_content"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!"}, {"name": "content", "type": "text", "required": true, "ui_config": {"label": "Content", "widget": "rich_text", "placeholder": "Enter content"}, "graphql_type": "String!"}]}