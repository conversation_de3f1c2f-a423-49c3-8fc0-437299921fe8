[{"name": "bmi_for_men_women_children", "type": "many-to-one", "ui_config": {"display_field": "title"}, "graphql_type": "site_bmi_for_men_women_children", "source_field": "bmi_for_men_women_children_id", "target_field": "id", "graphql_field": "bmi_for_men_women_child", "target_component": "site_bmi_for_men_women_children"}, {"name": "bmi_below_healthy_above_points", "type": "one-to-many", "config": {"title": "Below Healthy Above Points", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "point_title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_below_healthy_above_points!]!", "source_field": "id", "target_field": "bmi_below_healthy_above_range_id", "graphql_field": "bmi_below_healthy_above_points", "target_component": "site_bmi_below_healthy_above_points"}]