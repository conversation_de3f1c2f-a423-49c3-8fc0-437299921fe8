{"table": {"name": "bmi_below_healthy_above_range", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_bmi_below_healthy_above_range"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "bmi_for_men_women_children_id", "type": "text", "required": true, "ui_config": {"label": "For Men, Women & Children", "widget": "relationship_select", "display_field": "title"}, "foreign_key": {"column": "id", "on_delete": "CASCADE", "hasura_table": "site_bmi_for_men_women_children"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!"}, {"name": "subtitle", "type": "text", "required": true, "ui_config": {"label": "Subtitle", "placeholder": "Enter subtitle"}, "graphql_type": "String!"}]}