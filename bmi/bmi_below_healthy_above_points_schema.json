{"table": {"name": "bmi_below_healthy_above_points", "primary_key": "id", "display_field": "point_title", "hasura_table_name": "site_bmi_below_healthy_above_points"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "max_length": 16, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "bmi_below_healthy_above_range_id", "type": "text", "required": true, "ui_config": {"label": "BMI Below Healthy Above Range", "widget": "relationship_select", "display_field": "title"}, "max_length": 16, "foreign_key": {"column": "id", "on_delete": "CASCADE", "hasura_table": "site_bmi_below_healthy_above_range"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "point_title", "type": "text", "required": true, "ui_config": {"label": "Point Title", "placeholder": "Enter point title"}, "graphql_type": "String!", "default_value": ""}, {"name": "point_content", "type": "text", "required": true, "ui_config": {"label": "Point Content", "widget": "rich_text", "placeholder": "Enter point content"}, "graphql_type": "String!", "default_value": ""}]}