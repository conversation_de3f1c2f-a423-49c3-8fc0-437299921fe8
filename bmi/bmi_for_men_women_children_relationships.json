[{"name": "bmi_content", "type": "many-to-one", "ui_config": {"display_field": "title"}, "graphql_type": "site_bmi_content", "source_field": "bmi_id", "target_field": "id", "graphql_field": "bmi_content", "target_component": "site_bmi_content"}, {"name": "bmi_below_healthy_above_range", "type": "one-to-many", "config": {"title": "Below Healthy Above Range", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_below_healthy_above_ranges!]!", "source_field": "id", "target_field": "bmi_for_men_women_children_id", "graphql_field": "bmi_below_healthy_above_ranges", "target_component": "site_bmi_below_healthy_above_range"}]