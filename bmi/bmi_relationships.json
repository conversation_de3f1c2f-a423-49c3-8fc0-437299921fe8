[{"name": "bmi_faqs", "type": "one-to-many", "config": {"title": "FAQs", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "question", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_faqs!]!", "source_field": "id", "target_field": "bmi_id", "graphql_field": "bmi_faqs", "target_component": "site_bmi_faqs"}, {"name": "bmi_related_insurance_plans", "type": "one-to-many", "config": {"title": "Related Insurance Plan", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_related_insurance_plans!]!", "source_field": "id", "target_field": "bmi_id", "graphql_field": "bmi_related_insurance_plans", "target_component": "site_bmi_related_insurance_plan"}, {"name": "bmi_based_health_insurance_products", "type": "one-to-many", "config": {"title": "Based Health Insurance Products", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_based_health_insurance_products!]!", "source_field": "id", "target_field": "bmi_id", "graphql_field": "bmi_based_health_insurance_products", "target_component": "site_bmi_based_health_insurance_products"}, {"name": "bmi_testimonials", "type": "one-to-many", "config": {"title": "Testimonials", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "name", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_testimonials!]!", "source_field": "id", "target_field": "bmi_id", "graphql_field": "bmi_testimonials", "target_component": "site_bmi_testimonials"}, {"name": "bmi_for_men_women_children", "type": "one-to-many", "config": {"title": "For Men, Women & Children", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[bmi_for_men_women_children!]!", "source_field": "id", "target_field": "bmi_id", "graphql_field": "bmi_for_men_women_children", "target_component": "site_bmi_for_men_women_children"}]