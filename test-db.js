import { configDotenv } from "dotenv";
import pkg from "pg";
const { Pool } = pkg;

// Initialize environment variables
configDotenv();

// PostgreSQL configuration
const dbConfig = {
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

// Create PostgreSQL pool
const pool = new Pool(dbConfig);

async function testDatabaseConnection() {
  try {
    console.log('🔌 Testing database connection...');
    console.log(`📍 Connecting to: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    console.log(`👤 User: ${dbConfig.user}`);
    console.log(`🔒 SSL: ${dbConfig.ssl ? 'enabled' : 'disabled'}`);
    
    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Test query
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log(`⏰ Current time: ${result.rows[0].current_time}`);
    console.log(`🐘 PostgreSQL version: ${result.rows[0].postgres_version}`);
    
    // Check if schema exists
    const schemaCheck = await client.query(`
      SELECT schema_name 
      FROM information_schema.schemata 
      WHERE schema_name = 'cms_config'
    `);
    
    if (schemaCheck.rows.length > 0) {
      console.log('✅ cms_config schema exists');
      
      // Check if table exists
      const tableCheck = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'cms_config' 
        AND table_name = 'page_schemas'
      `);
      
      if (tableCheck.rows.length > 0) {
        console.log('✅ cms_config.page_schemas table exists');
        
        // Count existing records
        const countResult = await client.query('SELECT COUNT(*) as count FROM cms_config.page_schemas');
        console.log(`📊 Existing records: ${countResult.rows[0].count}`);
      } else {
        console.log('ℹ️  cms_config.page_schemas table does not exist (will be created during migration)');
      }
    } else {
      console.log('ℹ️  cms_config schema does not exist (will be created during migration)');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.code) {
      console.error(`   Code: ${error.code}`);
    }
    
    console.error('\n💡 Please check your .env file and ensure:');
    console.error('   - Database server is running');
    console.error('   - Credentials are correct');
    console.error('   - Database exists');
    console.error('   - User has proper permissions');
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the test
testDatabaseConnection();
