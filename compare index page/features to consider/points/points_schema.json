{"table": {"name": "compare_index_page_features_to_consider_points", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_compare_index_page_features_to_consider_points"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_features_to_consider_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Features To Consider", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_features_to_consider"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "Benefits", "value": "benefits"}, {"label": "Limitations", "value": "limitations"}]}, "graphql_type": "String!"}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "points", "type": "text[]", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "description": "Array of strings describing points", "placeholder": "Enter array of points"}, "graphql_type": "[String!]!", "default_value": []}]}