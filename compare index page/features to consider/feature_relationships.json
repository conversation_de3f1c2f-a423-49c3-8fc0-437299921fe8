[{"name": "compare_index_page_static_content", "type": "one-to-one", "config": {"display_field": "hero_title"}, "graphql_type": "compare_index_page_static_content", "source_field": "compare_index_page_static_content_id", "target_field": "id", "graphql_field": "compare_index_page_static_content", "target_component": "compare_index_page_static_content"}, {"name": "compare_index_page_features_to_consider_points", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[compare_index_page_features_to_consider_points!]!", "source_field": "id", "target_field": "compare_index_page_features_to_consider_id", "graphql_field": "compare_index_page_features_to_consider_points", "target_component": "compare_index_page_features_to_consider_points"}]