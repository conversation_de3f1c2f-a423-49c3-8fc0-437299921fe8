{"table": {"name": "compare_index_page_top_comparison_cards", "primary_key": "id", "display_field": "variant_one_id", "hasura_table_name": "site_compare_index_page_top_comparison_cards"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_top_comparisons_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Top Comparisons", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_top_comparisons"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "variant_one_id", "type": "text", "required": true, "ui_config": {"label": "Variant One", "widget": "relationship_select", "display_field": "variant_name"}, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!"}, {"name": "variant_two_id", "type": "text", "required": true, "ui_config": {"label": "Variant Two", "widget": "relationship_select", "display_field": "variant_name"}, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!"}]}