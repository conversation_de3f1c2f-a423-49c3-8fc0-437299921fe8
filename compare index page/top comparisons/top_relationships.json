[{"name": "compare_index_page_static_content", "type": "many-to-one", "config": {"display_field": "hero_title"}, "graphql_type": "compare_index_page_static_content", "source_field": "compare_index_page_static_content_id", "target_field": "id", "graphql_field": "compare_index_page_static_content", "target_component": "compare_index_page_static_content"}, {"name": "compare_index_page_top_comparison_cards", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "variant_one_name"}, "graphql_type": "[compare_index_page_top_comparison_cards!]!", "source_field": "id", "target_field": "compare_index_page_top_comparisons_id", "graphql_field": "compare_index_page_top_comparison_cards", "target_component": "compare_index_page_top_comparison_cards"}]