{"table": {"name": "compare_index_page_top_comparisons", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_compare_index_page_top_comparisons"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_static_content_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Static Content", "widget": "relationship_select", "display_field": "hero_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_static_content"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}]}