{"table": {"name": "compare_index_page_seo", "primary_key": "id", "display_field": "meta_title", "hasura_table_name": "site_compare_index_page_seo"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_static_content_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Static Content", "widget": "relationship_select", "display_field": "hero_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_static_content"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "meta_title", "type": "text", "required": true, "ui_config": {"label": "Meta Title", "placeholder": "Enter meta title"}, "graphql_type": "String!"}, {"name": "meta_description", "type": "text", "required": true, "ui_config": {"label": "Meta Description", "placeholder": "Enter meta description"}, "graphql_type": "String!"}, {"name": "keywords", "type": "text", "required": true, "ui_config": {"label": "Keywords", "placeholder": "Enter keywords"}, "graphql_type": "String!"}, {"name": "canonical", "type": "text", "required": true, "ui_config": {"label": "Canonical", "placeholder": "Enter canonical"}, "graphql_type": "String!"}, {"name": "prevent_indexing", "type": "boolean", "required": true, "ui_config": {"label": "Prevent Indexing", "widget": "checkbox"}, "graphql_type": "Boolean!", "default_value": false}]}