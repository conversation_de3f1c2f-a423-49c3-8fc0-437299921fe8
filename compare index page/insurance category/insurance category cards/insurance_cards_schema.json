{"table": {"name": "compare_index_page_insurance_category_cards", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_compare_index_page_insurance_category_cards"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_insurance_categories_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Insurance Categories", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_insurance_categories"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "points", "type": "text[]", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "description": "Array of strings describing points", "placeholder": "Enter array of points"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "button_text", "type": "text", "ui_config": {"label": "Button Text", "placeholder": "Enter button text"}, "graphql_type": "String!", "default_value": ""}]}