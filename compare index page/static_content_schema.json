{"table": {"name": "compare_index_page_static_content", "primary_key": "id", "display_field": "hero_title", "hasura_table_name": "site_compare_index_page_static_content"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "hero_title", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_description", "type": "text", "required": true, "ui_config": {"label": "Hero Description", "widget": "rich_text", "placeholder": "Enter hero description"}, "graphql_type": "String!", "default_value": ""}, {"name": "pill_content", "type": "text", "required": true, "ui_config": {"label": "Pill Content", "placeholder": "Enter pill content"}, "graphql_type": "String!", "default_value": ""}, {"name": "note", "type": "text", "required": true, "ui_config": {"label": "Note", "placeholder": "Enter note"}, "graphql_type": "String!", "default_value": ""}, {"name": "need_to_compare_title", "type": "text", "required": true, "ui_config": {"label": "Need to Compare Title", "placeholder": "Enter need to compare title"}, "graphql_type": "String!", "default_value": ""}, {"name": "need_to_compare_description", "type": "text", "required": true, "ui_config": {"label": "Need to Compare Description", "widget": "rich_text", "placeholder": "Enter need to compare description"}, "graphql_type": "String!", "default_value": ""}]}