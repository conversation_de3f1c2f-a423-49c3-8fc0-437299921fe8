{"table": {"name": "compare_index_page_sections", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_compare_index_page_sections"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "compare_index_page_static_content_id", "type": "text", "required": true, "ui_config": {"label": "Compare Index Page Static Content", "widget": "relationship_select", "display_field": "hero_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_compare_index_page_static_content"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Section Type", "widget": "select", "options": [{"label": "Assess Healthcare Need", "value": "assess_healthcare_need"}, {"label": "Read Inclusion Exclusion", "value": "read_inclusion_exclusion"}, {"label": "Review Claim Settlement Ratio", "value": "review_claim_settlement_ratio"}, {"label": "Policy Conditions And Addons", "value": "policy_conditions"}, {"label": "Mistakes to Avoid", "value": "mistakes_to_avoid"}, {"label": "Why Choose Our Expert", "value": "why_chose_our_expert"}, {"label": "What Experts Help You With", "value": "what_experts_help_you_with"}, {"label": "FAQs", "value": "faqs"}]}, "graphql_type": "String!"}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}]}