[{"name": "health_insurer_renewal_steps", "type": "many-to-one", "config": {"display_field": "title"}, "graphql_type": "health_insurer_renewal_steps", "source_field": "health_insurer_renewal_steps_id", "target_field": "id", "graphql_field": "health_insurer_renewal_steps", "target_component": "health_insurer_renewal_steps"}, {"name": "health_insurer_renewal_type_steps", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_renewal_type_steps!]!", "source_field": "id", "target_field": "health_insurer_renewal_type_id", "graphql_field": "health_insurer_renewal_type_steps", "target_component": "health_insurer_renewal_type_steps"}]