{"table": {"name": "health_insurer_renewal_type_steps", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_health_insurer_renewal_type_steps"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_renewal_type_id", "type": "text", "required": true, "ui_config": {"label": "Insurer Renewal Type", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_health_insurer_renewal_type"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}, {"name": "sequence", "type": "integer", "required": true, "ui_config": {"label": "Order", "placeholder": "Enter sequence"}, "graphql_type": "Int!", "default_value": 0}]}