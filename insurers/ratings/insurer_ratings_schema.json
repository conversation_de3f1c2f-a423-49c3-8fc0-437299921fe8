{"table": {"name": "health_insurer_ratings", "primary_key": "id", "display_field": "type", "hasura_table_name": "site_health_insurer_ratings"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Rating Type", "widget": "select", "options": [{"label": "Solvency", "value": "solvency"}, {"label": "ICR", "value": "icr"}, {"label": "Growth", "value": "growth"}, {"label": "AUM", "value": "aum"}]}, "graphql_type": "String!"}, {"name": "max_value", "type": "decimal", "required": true, "ui_config": {"label": "Max Value", "placeholder": "Enter max value"}, "graphql_type": "Float!"}, {"name": "min_value", "type": "decimal", "required": true, "ui_config": {"label": "Min Value", "placeholder": "Enter min value"}, "graphql_type": "Float!"}, {"name": "max_rating", "type": "decimal", "required": true, "ui_config": {"label": "<PERSON>", "placeholder": "Enter max rating"}, "graphql_type": "Float!"}, {"name": "min_rating", "type": "decimal", "required": true, "ui_config": {"label": "<PERSON>", "placeholder": "Enter min rating"}, "graphql_type": "Float!"}]}