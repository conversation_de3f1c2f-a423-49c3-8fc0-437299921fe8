[{"name": "health_insurer", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "health_insurer", "source_field": "health_insurer_id", "target_field": "id", "graphql_field": "health_insurer", "target_component": "health_insurers"}, {"name": "health_product_variant", "type": "many-to-one", "config": {"display_field": "variant_name"}, "graphql_type": "health_product_variant", "source_field": "health_variant_id", "target_field": "id", "graphql_field": "health_product_variant", "target_component": "health_product_variants"}]