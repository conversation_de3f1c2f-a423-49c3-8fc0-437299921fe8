[{"name": "health_insurer_claim_settlement", "type": "many-to-one", "ui_config": {"display_field": "title", "display_in_list": true}, "graphql_type": "health_insurer_claim_settlement", "source_field": "health_insurer_claim_settlement_id", "target_field": "id", "graphql_field": "health_insurer_claim_settlement", "target_component": "health_insurer_claim_settlement"}, {"name": "health_insurer_claim_settlement_steps", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_claim_settlement_steps!]!", "source_field": "id", "target_field": "health_insurer_claim_settlement_type_id", "graphql_field": "health_insurer_claim_settlement_steps", "target_component": "health_insurer_claim_settlement_steps"}]