{"table": {"name": "health_insurer_statistics", "primary_key": "id", "display_field": "health_insurer_id", "hasura_table_name": "site_health_insurer_statistics"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "Gross Direct Premium", "value": "gross direct premium"}, {"label": "ICR", "value": "icr"}, {"label": "Premium Underwritten", "value": "premium underwritten"}, {"label": "Solvency Ratio", "value": "solvency ratio"}]}, "graphql_type": "String!"}, {"name": "industry", "type": "decimal", "required": true, "ui_config": {"label": "Industry", "placeholder": "Enter industry"}, "graphql_type": "Float!"}, {"name": "company", "type": "decimal", "required": true, "ui_config": {"label": "Company", "placeholder": "Enter company"}, "graphql_type": "Float!"}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}]}