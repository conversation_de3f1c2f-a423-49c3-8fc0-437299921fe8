[{"name": "health_insurer", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "health_insurer", "source_field": "health_insurer_id", "target_field": "id", "graphql_field": "health_insurer", "target_component": "health_insurers"}, {"name": "site_health_insurer_why_choose_us_cards", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[site_health_insurer_why_choose_us_cards!]!", "source_field": "id", "target_field": "health_insurer_why_choose_us_id", "graphql_field": "site_health_insurer_why_choose_us_cards", "target_component": "site_health_insurer_why_choose_us_cards"}]