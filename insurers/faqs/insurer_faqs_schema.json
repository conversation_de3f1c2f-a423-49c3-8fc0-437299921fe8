{"table": {"name": "health_insurer_faqs", "primary_key": "id", "display_field": "question", "hasura_table_name": "site_health_insurer_faqs"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "question", "type": "text", "required": true, "ui_config": {"label": "Question", "widget": "textarea", "placeholder": "Enter FAQ question"}, "graphql_type": "String!", "default_value": ""}, {"name": "answer", "type": "text", "required": true, "ui_config": {"label": "Answer", "widget": "rich_text", "grid_cols": 2, "placeholder": "e.g., Answer for above question"}, "graphql_type": "String!", "default_value": ""}, {"name": "faq_type", "type": "enum", "required": true, "ui_config": {"label": "FAQ Type", "widget": "select", "options": [{"label": "General", "value": "general"}, {"label": "Claim Process", "value": "claim-process"}, {"label": "Renewal Process", "value": "renewal-process"}, {"label": "Claim Settlement Process", "value": "claim-settlement-process"}]}, "graphql_type": "String!"}, {"name": "sequence", "type": "integer", "required": true, "ui_config": {"label": "Order", "placeholder": "Enter sequence"}, "graphql_type": "Int!", "default_value": 0}]}