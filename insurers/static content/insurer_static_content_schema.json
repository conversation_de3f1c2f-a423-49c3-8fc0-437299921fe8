{"table": {"name": "health_insurer_static_content", "primary_key": "id", "display_field": "health_insurer_id", "hasura_table_name": "site_health_insurer_static_content"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "hero_title", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_description", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title", "widget": "rich_text"}, "graphql_type": "String!", "default_value": ""}, {"name": "verdict", "type": "text", "required": true, "ui_config": {"label": "Verdict", "widget": "rich_text", "placeholder": "Enter verdict content"}, "graphql_type": "String!", "default_value": ""}, {"name": "legacy", "type": "text", "required": true, "ui_config": {"label": "Legacy", "widget": "rich_text", "placeholder": "Enter legacy content"}, "graphql_type": "String!", "default_value": ""}, {"name": "kyc_docs", "type": "text[]", "required": true, "ui_config": {"label": "KYC Docs", "widget": "text_array", "description": "Array of strings describing KYC documents", "placeholder": "Enter array of KYC documents"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "customer_support_email", "type": "text", "required": true, "ui_config": {"label": "Customer Support Email", "placeholder": "Enter customer support email"}, "graphql_type": "String!", "default_value": ""}, {"name": "customer_support_number", "type": "text", "required": true, "ui_config": {"label": "Customer Support Number", "placeholder": "Enter customer support number"}, "graphql_type": "String!", "default_value": ""}, {"name": "renewal_key_points", "type": "text[]", "required": true, "ui_config": {"label": "Renewal Key Points", "widget": "text_array", "description": "Array of strings describing renewal key points", "placeholder": "Enter array of renewal key points"}, "graphql_type": "[String!]!", "default_value": []}]}