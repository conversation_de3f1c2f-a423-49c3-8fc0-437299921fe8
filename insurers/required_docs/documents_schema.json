{"table": {"name": "health_insurer_documents", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_health_insurer_documents"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "required_documents", "type": "text[]", "required": true, "ui_config": {"label": "Required Documents", "widget": "text_array", "description": "Array of strings describing required documents", "placeholder": "Enter array of required documents"}, "graphql_type": "[String!]!", "default_value": []}]}