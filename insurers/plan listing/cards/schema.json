{"table": {"name": "site_health_insurer_plan_listing_cards", "primary_key": "id", "display_field": "health_insurer_plan_listing_id", "hasura_table_name": "site_health_insurer_plan_listing_cards"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_plan_listing_id", "type": "text", "required": true, "ui_config": {"label": "Insurer Plan Listing", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_health_insurer_plan_listing"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "health_variant_id", "type": "text", "required": true, "ui_config": {"label": "Health Variant", "widget": "relationship_select", "display_field": "variant_name"}, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!"}]}