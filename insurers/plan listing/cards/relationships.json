[{"name": "site_health_insurer_plan_listing", "type": "many-to-one", "config": {"display_field": "title"}, "graphql_type": "site_health_insurer_plan_listing", "source_field": "site_health_insurer_plan_listing_id", "target_field": "id", "graphql_field": "site_health_insurer_plan_listing", "target_component": "site_health_insurer_plan_listing"}, {"name": "health_product_variant", "type": "many-to-one", "config": {"display_field": "variant_name"}, "graphql_type": "health_product_variant", "source_field": "health_variant_id", "target_field": "id", "graphql_field": "health_product_variant", "target_component": "health_product_variants"}]