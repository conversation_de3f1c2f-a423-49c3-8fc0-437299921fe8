[{"name": "health_insurer_static_content", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "hero_title"}, "graphql_type": "health_insurer_static_content", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_static_content", "target_component": "health_insurer_static_content"}, {"name": "health_insurer_faqs", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "question"}, "graphql_type": "[health_insurer_faqs!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_faqs", "target_component": "health_insurer_faqs"}, {"name": "health_insurer_claim_settlements", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_claim_settlements!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_claim_settlements", "target_component": "health_insurer_claim_settlement"}, {"name": "health_insurer_seo", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title"}, "graphql_type": "health_insurer_seo", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_seo", "target_component": "health_insurer_seo"}, {"name": "health_insurer_policy_guides", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_policy_guides!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_policy_guides", "target_component": "health_insurer_policy_guides"}, {"name": "health_insurer_pros_cons", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_pros_cons!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_pros_cons", "target_component": "health_insurer_pros_cons"}, {"name": "health_insurer_renewal_steps", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_renewal_steps!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_renewal_steps", "target_component": "health_insurer_renewal_steps"}, {"name": "health_insurer_statistics", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "type"}, "graphql_type": "[health_insurer_statistics!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_statistics", "target_component": "health_insurer_statistics"}, {"name": "health_insurer_expert_reviews", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_expert_reviews!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_expert_reviews", "target_component": "health_insurer_expert_review"}, {"name": "health_insurer_testimonials", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "name"}, "graphql_type": "[health_insurer_testimonials!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_testimonials", "target_component": "health_insurer_testimonials"}, {"name": "health_insurer_insurance_types", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_insurance_types!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_insurance_types", "target_component": "health_insurer_insurance_types"}, {"name": "health_insurer_network_hospital_detail", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "network_hospital_count"}, "graphql_type": "health_insurer_network_hospital_detail", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_network_hospital_detail", "target_component": "health_insurer_network_hospital_detail"}, {"name": "health_insurer_plans", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "name"}, "graphql_type": "[health_insurer_plans!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_plans", "target_component": "health_insurer_plans"}, {"name": "health_insurer_documents", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_documents!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_documents", "target_component": "health_insurer_documents"}, {"name": "health_insurer_why_choose_us", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_why_choose_us!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_why_choose_us", "target_component": "site_health_insurer_why_choose_us"}, {"name": "health_insurer_plan_listing", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "health_variant"}, "graphql_type": "[health_insurer_plan_listing!]!", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "health_insurer_plan_listing", "target_component": "site_health_insurer_plan_listing"}, {"name": "health_insurer_plan_listing_seo", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title"}, "graphql_type": "site_health_insurer_plan_listing_seo", "source_field": "id", "target_field": "health_insurer_id", "graphql_field": "site_health_insurer_plan_listing_seo", "target_component": "site_health_insurer_plan_listing_seo"}]