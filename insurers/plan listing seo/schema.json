{"table": {"name": "site_health_insurer_plan_listing_seo", "primary_key": "id", "display_field": "meta_title", "hasura_table_name": "site_health_insurer_plan_listing_seo"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "meta_title", "type": "text", "required": true, "ui_config": {"label": "Meta Title", "placeholder": "Enter meta title"}, "graphql_type": "String!"}, {"name": "meta_description", "type": "text", "required": true, "ui_config": {"label": "Meta Description", "placeholder": "Enter meta description"}, "graphql_type": "String!"}, {"name": "meta_keyword", "type": "text", "required": true, "ui_config": {"label": "<PERSON>a Keyword", "placeholder": "Enter meta keyword"}, "graphql_type": "String!"}, {"name": "prevent_indexing", "type": "boolean", "required": true, "ui_config": {"label": "Prevent Indexing", "widget": "checkbox"}, "graphql_type": "Boolean!", "default_value": false}, {"name": "canonical", "type": "text", "required": false, "ui_config": {"label": "Canonical", "placeholder": "Enter canonical"}, "graphql_type": "String"}]}