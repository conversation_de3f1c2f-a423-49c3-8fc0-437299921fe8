{"table": {"name": "site_blog_categories", "primary_key": "id", "display_field": "name", "hasura_table_name": "site_blog_categories"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Name", "placeholder": "Enter name"}, "graphql_type": "String!", "default_value": ""}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "subtitle", "type": "text", "required": true, "ui_config": {"label": "Subtitle", "placeholder": "Enter subtitle"}, "graphql_type": "String!", "default_value": ""}, {"name": "slug", "type": "text", "required": true, "ui_config": {"label": "Slug", "placeholder": "Enter slug"}, "graphql_type": "String!", "default_value": ""}, {"name": "icon_key", "type": "text", "required": true, "ui_config": {"widget": "file_upload", "label": "Icon Key", "placeholder": "Enter icon key"}, "graphql_type": "String!", "default_value": ""}]}