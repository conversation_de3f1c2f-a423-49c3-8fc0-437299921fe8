{"table": {"name": "blogs", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_blogs"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "blog_category_id", "type": "text", "required": true, "ui_config": {"label": "Blog Category", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"column": "id", "hasura_table": "site_blog_categories"}, "graphql_type": "String!"}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "subtitle", "type": "text", "ui_config": {"label": "Subtitle", "placeholder": "Enter subtitle"}, "graphql_type": "String!", "default_value": ""}, {"name": "slug", "type": "text", "required": true, "ui_config": {"label": "Slug", "placeholder": "Enter slug"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}, {"name": "reading_time", "type": "text", "ui_config": {"label": "Reading Time", "placeholder": "Enter reading time"}, "graphql_type": "String!", "default_value": ""}, {"name": "top_blog", "type": "boolean", "required": true, "ui_config": {"label": "Top Blog", "widget": "checkbox"}, "graphql_type": "Boolean!", "default_value": false}, {"name": "thumbnail_key", "type": "text", "required": true, "ui_config": {"widget": "file_upload", "label": "<PERSON><PERSON><PERSON><PERSON> Key", "placeholder": "Enter thumbnail key"}, "graphql_type": "String!", "default_value": ""}, {"name": "author_id", "type": "text", "required": true, "ui_config": {"label": "Author", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"column": "id", "hasura_table": "site_blog_author"}, "graphql_type": "String!"}]}