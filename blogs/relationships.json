[{"name": "blog_category", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "blog_category", "source_field": "blog_category_id", "target_field": "id", "graphql_field": "blog_category", "target_component": "site_blog_categories"}, {"name": "blog_author", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "blog_author", "source_field": "author_id", "target_field": "id", "graphql_field": "blog_author", "target_component": "site_blog_author"}, {"name": "blog_related_reads", "type": "one-to-many", "config": {"display_field": "title"}, "graphql_type": "[blog_related_reads!]!", "source_field": "id", "target_field": "related_blog_id", "graphql_field": "blog_related_reads", "target_component": "site_blog_related_reads"}, {"name": "blog_related_term_variants", "type": "one-to-many", "config": {"display_field": "term_variant_id"}, "graphql_type": "[blog_related_term_variants!]!", "source_field": "id", "target_field": "blog_id", "graphql_field": "blog_related_term_variants", "target_component": "site_blog_related_term_variants"}, {"name": "blog_related_health_variants", "type": "one-to-many", "config": {"display_field": "health_variant_id"}, "graphql_type": "[blog_related_health_variants!]!", "source_field": "id", "target_field": "blog_id", "graphql_field": "blog_related_health_variants", "target_component": "site_blog_related_health_variants"}, {"name": "blog_seo", "type": "one-to-one", "config": {"display_field": "meta_title"}, "graphql_type": "blog_seo", "source_field": "id", "target_field": "blog_id", "graphql_field": "blog_seo", "target_component": "site_blogs_seo"}]