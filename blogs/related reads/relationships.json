[{"name": "related_blog", "type": "many-to-one", "config": {"display_field": "title"}, "graphql_type": "related_blog", "source_field": "related_blog_id", "target_field": "id", "graphql_field": "related_blog", "target_component": "blogs"}, {"name": "blog", "type": "many-to-one", "config": {"display_field": "title"}, "graphql_type": "blog", "source_field": "blog_id", "target_field": "id", "graphql_field": "blog", "target_component": "blogs"}]