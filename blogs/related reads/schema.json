{"table": {"name": "site_blog_related_reads", "primary_key": "id", "display_field": "related_blog_id", "hasura_table_name": "site_blog_related_reads"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "blog_id", "type": "text", "required": true, "ui_config": {"label": "Blog", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_blogs"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "related_blog_id", "type": "text", "required": true, "ui_config": {"label": "Related Blog", "widget": "relationship_select", "display_field": "title"}, "foreign_key": {"column": "id", "hasura_table": "site_blogs"}, "graphql_type": "String!"}]}