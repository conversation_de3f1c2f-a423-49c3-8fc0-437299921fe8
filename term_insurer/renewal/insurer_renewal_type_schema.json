{"table": {"name": "health_insurer_renewal_type", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_health_insurer_renewal_type"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_renewal_steps_id", "type": "text", "required": true, "ui_config": {"label": "Insurer <PERSON>", "widget": "relationship_select", "display_field": "title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_health_insurer_renewal_steps"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "None", "value": "none"}, {"label": "Online Renewal", "value": "online renewal"}, {"label": "Offline Renewal", "value": "offline renewal"}]}, "graphql_type": "String!"}]}