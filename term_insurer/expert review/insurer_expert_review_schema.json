{"table": {"name": "health_insurer_expert_review", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_health_insurer_expert_review"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}, {"name": "what_we_like", "type": "text[]", "required": true, "ui_config": {"label": "What We Like", "widget": "text_array", "description": "Array of strings describing what we like", "placeholder": "Enter array of what we like"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "improvement_areas", "type": "text[]", "required": true, "ui_config": {"label": "Improvement Areas", "widget": "text_array", "description": "Array of strings describing improvement areas", "placeholder": "Enter array of improvement areas"}, "graphql_type": "[String!]!", "default_value": []}]}