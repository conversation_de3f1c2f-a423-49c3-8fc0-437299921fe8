{"table": {"name": "health_insurer_network_hospital_detail", "primary_key": "id", "display_field": "health_insurer_id", "hasura_table_name": "site_health_insurer_network_hospital_detail"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "network_hospital_count", "type": "text", "required": true, "ui_config": {"label": "Network Hospital Count", "placeholder": "Enter network hospital count"}, "graphql_type": "String!", "default_value": ""}, {"name": "cities_covered", "type": "text", "required": true, "ui_config": {"label": "Cities Covered", "placeholder": "Enter cities covered"}, "graphql_type": "String!", "default_value": ""}, {"name": "states_and_ut", "type": "text", "required": true, "ui_config": {"label": "States and UT", "placeholder": "Enter states and UT"}, "graphql_type": "String!", "default_value": ""}]}