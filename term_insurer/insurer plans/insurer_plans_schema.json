{"table": {"name": "health_insurer_plans", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_health_insurer_plans"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "health_variant_id", "type": "text", "required": true, "ui_config": {"label": "Health Variant", "widget": "relationship_select", "display_field": "variant_name"}, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!"}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}, {"name": "points", "type": "text[]", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "description": "Array of strings describing points", "placeholder": "Enter array of points"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "plan_type", "type": "enum", "required": true, "ui_config": {"label": "Plan Type", "widget": "select", "options": [{"label": "Top", "value": "top"}, {"label": "Super Topup", "value": "super_topup"}]}, "graphql_type": "String!"}]}