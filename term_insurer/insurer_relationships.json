[{"name": "term_insurer_static_content", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "hero_title"}, "graphql_type": "term_insurer_static_content", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_static_content", "target_component": "term_insurer_static_content"}, {"name": "term_insurer_faqs", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "question"}, "graphql_type": "[term_insurer_faqs!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_faqs", "target_component": "term_insurer_faqs"}, {"name": "term_insurer_claim_settlements", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[term_insurer_claim_settlements!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_claim_settlements", "target_component": "term_insurer_claim_settlement"}, {"name": "term_insurer_seo", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title"}, "graphql_type": "term_insurer_seo", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_seo", "target_component": "term_insurer_seo"}, {"name": "term_insurer_policy_guides", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[term_insurer_policy_guides!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_policy_guides", "target_component": "term_insurer_policy_guides"}, {"name": "term_insurer_pros_cons", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[term_insurer_pros_cons!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_pros_cons", "target_component": "term_insurer_pros_cons"}, {"name": "term_insurer_renewal_steps", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[term_insurer_renewal_steps!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_renewal_steps", "target_component": "term_insurer_renewal_steps"}, {"name": "term_insurer_ratings", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "solvency"}, "graphql_type": "[term_insurer_ratings!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_ratings", "target_component": "term_insurer_ratings"}, {"name": "term_insurer_statistics", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "type"}, "graphql_type": "[term_insurer_statistics!]!", "source_field": "id", "target_field": "term_insurer_id", "graphql_field": "term_insurer_statistics", "target_component": "term_insurer_statistics"}]