{"table": {"name": "health_insurer_ratings", "primary_key": "id", "display_field": "insurer_id", "hasura_table_name": "site_health_insurer_ratings"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "health_insurers"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "solvency", "type": "decimal", "required": true, "ui_config": {"label": "Solvency", "placeholder": "Enter solvency"}, "graphql_type": "Float!"}, {"name": "icr", "type": "decimal", "required": true, "ui_config": {"label": "ICR", "placeholder": "Enter ICR"}, "graphql_type": "Float!"}, {"name": "growth", "type": "decimal", "required": true, "ui_config": {"label": "Growth", "placeholder": "Enter growth"}, "graphql_type": "Float!"}, {"name": "aum", "type": "decimal", "required": true, "ui_config": {"label": "AUM", "placeholder": "Enter AUM"}, "graphql_type": "Float!"}, {"name": "one_assure_rating", "type": "decimal", "required": true, "ui_config": {"label": "OneAssure Rating", "placeholder": "Enter OneAssure Rating"}, "graphql_type": "Float!"}]}