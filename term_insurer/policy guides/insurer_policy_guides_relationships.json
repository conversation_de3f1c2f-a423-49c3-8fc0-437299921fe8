[{"name": "health_insurer", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "health_insurer", "source_field": "health_insurer_id", "target_field": "id", "graphql_field": "health_insurer", "target_component": "health_insurers"}, {"name": "health_insurer_policy_guide_points", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[health_insurer_policy_guide_points!]!", "source_field": "id", "target_field": "health_insurer_policy_guide_id", "graphql_field": "health_insurer_policy_guide_points", "target_component": "health_insurer_policy_guide_points"}]