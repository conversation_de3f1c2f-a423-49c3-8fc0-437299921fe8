[{"name": "influencer_testimonials", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "name"}, "graphql_type": "[influencer_testimonials!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_testimonials", "target_component": "influencer_testimonials"}, {"name": "influencer_faqs", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "question"}, "graphql_type": "[influencer_faqs!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_faqs", "target_component": "influencer_faqs"}, {"name": "influencer_why_trust_us", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[influencer_why_trust_us!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_why_trust_us", "target_component": "influencer_why_trust_us"}, {"name": "influencer_why_choose_one_assures", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[influencer_why_choose_one_assures!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_why_choose_one_assures", "target_component": "influencer_why_choose_one_assure"}, {"name": "influencer_one_assure_backgrounds", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[influencer_one_assure_backgrounds!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_one_assure_backgrounds", "target_component": "influencer_one_assure_background"}, {"name": "influencer_recommendations", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[influencer_recommendations!]!", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_recommendations", "target_component": "influencer_recommendations"}, {"name": "influencer_seo", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title"}, "graphql_type": "influencer_seo", "source_field": "id", "target_field": "influencer_id", "graphql_field": "influencer_seo", "target_component": "influencer_seo"}]