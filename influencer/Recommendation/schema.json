{"table": {"name": "influencer_recommendation", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_influencer_recommendation"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "influencer_id", "type": "text", "required": true, "ui_config": {"label": "Influencer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_influencer"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Name", "placeholder": "Enter name"}, "graphql_type": "String!", "default_value": ""}, {"name": "designation", "type": "text", "required": true, "ui_config": {"label": "Designation", "placeholder": "Enter designation"}, "graphql_type": "String!", "default_value": ""}, {"name": "content", "type": "text", "required": true, "ui_config": {"label": "Content", "widget": "rich_text", "placeholder": "Enter content"}, "graphql_type": "String!", "default_value": ""}, {"name": "image_url", "type": "text", "required": false, "ui_config": {"label": "Image URL", "placeholder": "https://example.com/image.png"}, "validation": {"url": true}, "graphql_type": "String"}, {"name": "pills_content", "type": "text[]", "required": true, "ui_config": {"label": "Pills Content", "widget": "text_array", "description": "Array of strings describing pills content", "placeholder": "Enter array of pills content"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "social_handles", "type": "text[]", "required": true, "ui_config": {"label": "Social Handles", "widget": "text_array", "description": "Array of strings describing social handles", "placeholder": "Enter array of social handles"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "video_title", "type": "text", "required": true, "ui_config": {"label": "Video Title", "placeholder": "Enter video title"}, "graphql_type": "String!", "default_value": ""}, {"name": "video_description", "type": "text", "required": true, "ui_config": {"label": "Video Description", "widget": "rich_text", "placeholder": "Enter video description"}, "graphql_type": "String!", "default_value": ""}, {"name": "video_url", "type": "text", "required": false, "ui_config": {"label": "Video URL", "placeholder": "https://example.com/video.mp4"}, "validation": {"url": true}, "graphql_type": "String"}]}