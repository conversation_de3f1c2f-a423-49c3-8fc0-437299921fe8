[{"name": "influencer", "type": "many-to-one", "config": {"display_field": "name"}, "graphql_type": "influencer", "source_field": "influencer_id", "target_field": "id", "graphql_field": "influencer", "target_component": "influencer"}, {"name": "influencer_why_choose_one_assure_points", "type": "one-to-many", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title"}, "graphql_type": "[influencer_why_choose_one_assure_points!]!", "source_field": "id", "target_field": "influencer_why_choose_one_assure_id", "graphql_field": "influencer_why_choose_one_assure_points", "target_component": "influencer_why_choose_one_assure_points"}]