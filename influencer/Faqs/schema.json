{"table": {"name": "influencer_faqs", "primary_key": "id", "display_field": "question", "hasura_table_name": "site_influencer_faqs"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "influencer_id", "type": "text", "required": true, "ui_config": {"label": "Influencer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_influencer"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "question", "type": "text", "required": true, "ui_config": {"label": "Question", "widget": "textarea", "placeholder": "Enter FAQ question"}, "graphql_type": "String!", "default_value": ""}, {"name": "answer", "type": "text", "required": true, "ui_config": {"label": "Answer", "widget": "rich_text", "grid_cols": 2, "placeholder": "e.g., Answer for above question"}, "graphql_type": "String!", "default_value": ""}]}