{"table": {"name": "influencer_testimonials", "primary_key": "id", "display_field": "name", "hasura_table_name": "site_influencer_testimonials"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "influencer_id", "type": "text", "required": true, "ui_config": {"label": "Influencer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_influencer"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Name", "placeholder": "Enter name"}, "graphql_type": "String!"}, {"name": "content", "type": "text", "required": true, "ui_config": {"label": "Content", "widget": "rich_text", "placeholder": "Enter content"}, "graphql_type": "String!"}]}