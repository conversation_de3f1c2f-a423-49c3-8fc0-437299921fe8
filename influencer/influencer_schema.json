{"table": {"name": "influencer", "primary_key": "id", "display_field": "name", "hasura_table_name": "site_influencer"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Name", "placeholder": "Enter name"}, "graphql_type": "String!"}, {"name": "slug", "type": "text", "required": true, "ui_config": {"label": "Slug", "placeholder": "Enter slug"}, "graphql_type": "String!"}, {"name": "data_access_link", "type": "text", "required": true, "ui_config": {"label": "Data Access Link", "placeholder": "/prost-technologies-private-limited/fixyourinsurance-anshumanoneassure?BackgroundColor=FFFFFF&BrandColor=098666&hideLHS=false"}, "graphql_type": "String!"}, {"name": "hero_title", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title"}, "graphql_type": "String!"}, {"name": "hero_description", "type": "text", "required": true, "ui_config": {"label": "Hero Description", "widget": "rich_text", "placeholder": "Enter hero description"}, "graphql_type": "String!"}, {"name": "hero_image_url", "type": "text", "required": false, "ui_config": {"widget": "file_upload", "label": "Hero Image URL", "placeholder": "https://example.com/image.png"}, "validation": {"url": true}, "graphql_type": "String"}]}