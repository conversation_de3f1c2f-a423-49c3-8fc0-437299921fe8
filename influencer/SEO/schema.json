{"table": {"name": "influencer_seo", "primary_key": "id", "display_field": "meta_title", "hasura_table_name": "site_influencer_seo"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "influencer_id", "type": "text", "required": true, "ui_config": {"label": "Influencer", "widget": "relationship_select", "display_field": "name", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_influencer"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "meta_title", "type": "text", "required": true, "ui_config": {"label": "Meta Title", "placeholder": "Enter meta title"}, "graphql_type": "String!"}, {"name": "meta_description", "type": "text", "required": true, "ui_config": {"label": "Meta Description", "placeholder": "Enter meta description"}, "graphql_type": "String!"}, {"name": "meta_keyword", "type": "text", "required": true, "ui_config": {"label": "<PERSON>a Keyword", "placeholder": "Enter meta keyword"}, "graphql_type": "String!"}, {"name": "prevent_indexing", "type": "boolean", "required": true, "ui_config": {"label": "Prevent Indexing", "widget": "checkbox"}, "graphql_type": "Boolean!", "default_value": false}, {"name": "source", "type": "enum", "required": true, "ui_config": {"label": "Source", "widget": "select", "options": [{"label": "None", "value": "none"}, {"label": "FB Campaign", "value": "fb-campaign"}, {"label": "Masterclass Campaign", "value": "masterclass-campaign"}]}, "graphql_type": "String!"}, {"name": "canonical", "type": "text", "required": true, "ui_config": {"label": "Canonical", "placeholder": "Enter canonical"}, "graphql_type": "String!"}]}