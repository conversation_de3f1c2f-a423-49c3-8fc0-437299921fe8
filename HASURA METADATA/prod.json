{"resource_version": 107, "metadata": {"version": 3, "sources": [{"name": "default", "kind": "postgres", "tables": [{"table": {"name": "compare_feature", "schema": "health"}, "object_relationships": [{"name": "compare_section", "using": {"foreign_key_constraint_on": "section_id"}}], "array_relationships": [{"name": "feature_values", "using": {"foreign_key_constraint_on": {"column": "feature_id", "table": {"name": "feature_value", "schema": "health"}}}}]}, {"table": {"name": "compare_section", "schema": "health"}, "array_relationships": [{"name": "compare_features", "using": {"foreign_key_constraint_on": {"column": "section_id", "table": {"name": "compare_feature", "schema": "health"}}}}]}, {"table": {"name": "feature_value", "schema": "health"}, "object_relationships": [{"name": "compare_feature", "using": {"foreign_key_constraint_on": "feature_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}, {"table": {"name": "insurers", "schema": "health"}, "object_relationships": [{"name": "health_insurer_network_hospital_detail", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_network_hospital_detail", "schema": "site"}}}}, {"name": "health_insurer_seo", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_seo", "schema": "site"}}}}, {"name": "health_insurer_static_content", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_static_content", "schema": "site"}}}}], "array_relationships": [{"name": "health_insurer_claim_settlements", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_claim_settlement", "schema": "site"}}}}, {"name": "health_insurer_documents", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_documents", "schema": "site"}}}}, {"name": "health_insurer_expert_reviews", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_expert_review", "schema": "site"}}}}, {"name": "health_insurer_faqs", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_faqs", "schema": "site"}}}}, {"name": "health_insurer_insurance_types", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_insurance_types", "schema": "site"}}}}, {"name": "health_insurer_plans", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_plans", "schema": "site"}}}}, {"name": "health_insurer_policy_guides", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_policy_guide", "schema": "site"}}}}, {"name": "health_insurer_pros_cons", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_pros_cons", "schema": "site"}}}}, {"name": "health_insurer_renewal_steps", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_renewal_steps", "schema": "site"}}}}, {"name": "health_insurer_statistics", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_statistics", "schema": "site"}}}}, {"name": "health_insurer_testimonials", "using": {"manual_configuration": {"column_mapping": {"id": "health_insurer_id"}, "insertion_order": null, "remote_table": {"name": "health_insurer_testimonials", "schema": "site"}}}}, {"name": "products", "using": {"foreign_key_constraint_on": {"column": "insurer_id", "table": {"name": "products", "schema": "health"}}}}]}, {"table": {"name": "product_rider", "schema": "health"}, "object_relationships": [{"name": "product", "using": {"foreign_key_constraint_on": "product_id"}}], "array_relationships": [{"name": "variantRidersByRiderId", "using": {"foreign_key_constraint_on": {"column": "rider_id", "table": {"name": "variant_rider", "schema": "health"}}}}, {"name": "variant_riders", "using": {"foreign_key_constraint_on": {"column": "dependent_rider_id", "table": {"name": "variant_rider", "schema": "health"}}}}]}, {"table": {"name": "product_variants", "schema": "health"}, "object_relationships": [{"name": "health_variant_seo", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_seo", "schema": "site"}}}}, {"name": "health_variant_static_content", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_static_content", "schema": "site"}}}}, {"name": "product", "using": {"foreign_key_constraint_on": "product_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "parent_variant_id"}}], "array_relationships": [{"name": "feature_values", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "feature_value", "schema": "health"}}}}, {"name": "healthVariantRelatedVariantsByHealthVariantId", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_related_variants", "schema": "site"}}}}, {"name": "health_variant_faqs", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_faqs", "schema": "site"}}}}, {"name": "health_variant_features", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_features", "schema": "site"}}}}, {"name": "health_variant_highlighted_features", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_highlighted_features", "schema": "site"}}}}, {"name": "health_variant_policy_docs", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_policy_docs", "schema": "site"}}}}, {"name": "health_variant_ratings", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_ratings", "schema": "site"}}}}, {"name": "health_variant_related_variants", "using": {"manual_configuration": {"column_mapping": {"id": "related_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_related_variants", "schema": "site"}}}}, {"name": "health_variant_whyoneassures", "using": {"manual_configuration": {"column_mapping": {"id": "health_variant_id"}, "insertion_order": null, "remote_table": {"name": "health_variant_whyoneassures", "schema": "site"}}}}, {"name": "product_variants", "using": {"foreign_key_constraint_on": {"column": "parent_variant_id", "table": {"name": "product_variants", "schema": "health"}}}}, {"name": "variant_riders", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "variant_rider", "schema": "health"}}}}]}, {"table": {"name": "products", "schema": "health"}, "object_relationships": [{"name": "insurer", "using": {"foreign_key_constraint_on": "insurer_id"}}], "array_relationships": [{"name": "product_riders", "using": {"foreign_key_constraint_on": {"column": "product_id", "table": {"name": "product_rider", "schema": "health"}}}}, {"name": "product_variants", "using": {"foreign_key_constraint_on": {"column": "product_id", "table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "variant_rider", "schema": "health"}, "object_relationships": [{"name": "productRiderByRiderId", "using": {"foreign_key_constraint_on": "rider_id"}}, {"name": "product_rider", "using": {"foreign_key_constraint_on": "dependent_rider_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}, {"table": {"name": "about", "schema": "site"}}, {"table": {"name": "about_founder", "schema": "site"}}, {"table": {"name": "about_impact", "schema": "site"}}, {"table": {"name": "about_impact_items", "schema": "site"}}, {"table": {"name": "about_investor_items", "schema": "site"}}, {"table": {"name": "about_investors", "schema": "site"}}, {"table": {"name": "about_journey", "schema": "site"}}, {"table": {"name": "blog_categories", "schema": "site"}}, {"table": {"name": "blog_category_seo", "schema": "site"}}, {"table": {"name": "blog_related_health_variants", "schema": "site"}}, {"table": {"name": "blog_related_reads", "schema": "site"}}, {"table": {"name": "blog_related_term_variants", "schema": "site"}}, {"table": {"name": "blogs", "schema": "site"}}, {"table": {"name": "blogs_seo", "schema": "site"}}, {"table": {"name": "bmi_based_health_insurance_products", "schema": "site"}, "object_relationships": [{"name": "bmi_content", "using": {"foreign_key_constraint_on": "bmi_id"}}]}, {"table": {"name": "bmi_below_healthy_above_points", "schema": "site"}, "object_relationships": [{"name": "bmi_below_healthy_above_range", "using": {"foreign_key_constraint_on": "bmi_below_healthy_above_range_id"}}]}, {"table": {"name": "bmi_below_healthy_above_range", "schema": "site"}, "object_relationships": [{"name": "bmi_for_men_women_child", "using": {"foreign_key_constraint_on": "bmi_for_men_women_children_id"}}], "array_relationships": [{"name": "bmi_below_healthy_above_points", "using": {"foreign_key_constraint_on": {"column": "bmi_below_healthy_above_range_id", "table": {"name": "bmi_below_healthy_above_points", "schema": "site"}}}}]}, {"table": {"name": "bmi_content", "schema": "site"}, "array_relationships": [{"name": "bmi_based_health_insurance_products", "using": {"foreign_key_constraint_on": {"column": "bmi_id", "table": {"name": "bmi_based_health_insurance_products", "schema": "site"}}}}, {"name": "bmi_faqs", "using": {"foreign_key_constraint_on": {"column": "bmi_id", "table": {"name": "bmi_faqs", "schema": "site"}}}}, {"name": "bmi_for_men_women_children", "using": {"foreign_key_constraint_on": {"column": "bmi_id", "table": {"name": "bmi_for_men_women_children", "schema": "site"}}}}, {"name": "bmi_related_insurance_plans", "using": {"foreign_key_constraint_on": {"column": "bmi_id", "table": {"name": "bmi_related_insurance_plan", "schema": "site"}}}}, {"name": "bmi_testimonials", "using": {"foreign_key_constraint_on": {"column": "bmi_id", "table": {"name": "bmi_testimonials", "schema": "site"}}}}]}, {"table": {"name": "bmi_faqs", "schema": "site"}, "object_relationships": [{"name": "bmi_content", "using": {"foreign_key_constraint_on": "bmi_id"}}]}, {"table": {"name": "bmi_for_men_women_children", "schema": "site"}, "object_relationships": [{"name": "bmi_content", "using": {"foreign_key_constraint_on": "bmi_id"}}], "array_relationships": [{"name": "bmi_below_healthy_above_ranges", "using": {"foreign_key_constraint_on": {"column": "bmi_for_men_women_children_id", "table": {"name": "bmi_below_healthy_above_range", "schema": "site"}}}}]}, {"table": {"name": "bmi_related_insurance_plan", "schema": "site"}, "object_relationships": [{"name": "bmi_content", "using": {"foreign_key_constraint_on": "bmi_id"}}]}, {"table": {"name": "bmi_testimonials", "schema": "site"}, "object_relationships": [{"name": "bmi_content", "using": {"foreign_key_constraint_on": "bmi_id"}}]}, {"table": {"name": "compare_index_page_features_to_consider", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_static_content", "using": {"foreign_key_constraint_on": "compare_index_page_static_content_id"}}], "array_relationships": [{"name": "compare_index_page_features_to_consider_points", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_features_to_consider_id", "table": {"name": "compare_index_page_features_to_consider_points", "schema": "site"}}}}]}, {"table": {"name": "compare_index_page_features_to_consider_points", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_features_to_consider", "using": {"foreign_key_constraint_on": "compare_index_page_features_to_consider_id"}}]}, {"table": {"name": "compare_index_page_insurance_categories", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_static_content", "using": {"foreign_key_constraint_on": "compare_index_page_static_content_id"}}], "array_relationships": [{"name": "compare_index_page_insurance_category_cards", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_insurance_categories_id", "table": {"name": "compare_index_page_insurance_category_cards", "schema": "site"}}}}]}, {"table": {"name": "compare_index_page_insurance_category_cards", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_insurance_category", "using": {"foreign_key_constraint_on": "compare_index_page_insurance_categories_id"}}]}, {"table": {"name": "compare_index_page_section_points", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_section", "using": {"foreign_key_constraint_on": "compare_index_page_section_id"}}]}, {"table": {"name": "compare_index_page_sections", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_static_content", "using": {"foreign_key_constraint_on": "compare_index_page_static_content_id"}}], "array_relationships": [{"name": "compare_index_page_section_points", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_section_id", "table": {"name": "compare_index_page_section_points", "schema": "site"}}}}]}, {"table": {"name": "compare_index_page_seo", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_static_content", "using": {"foreign_key_constraint_on": "compare_index_page_static_content_id"}}]}, {"table": {"name": "compare_index_page_static_content", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_seo", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_static_content_id", "table": {"name": "compare_index_page_seo", "schema": "site"}}}}], "array_relationships": [{"name": "compare_index_page_features_to_considers", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_static_content_id", "table": {"name": "compare_index_page_features_to_consider", "schema": "site"}}}}, {"name": "compare_index_page_insurance_categories", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_static_content_id", "table": {"name": "compare_index_page_insurance_categories", "schema": "site"}}}}, {"name": "compare_index_page_sections", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_static_content_id", "table": {"name": "compare_index_page_sections", "schema": "site"}}}}, {"name": "compare_index_page_top_comparisons", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_static_content_id", "table": {"name": "compare_index_page_top_comparisons", "schema": "site"}}}}]}, {"table": {"name": "compare_index_page_top_comparison_cards", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_top_comparison", "using": {"foreign_key_constraint_on": "compare_index_page_top_comparisons_id"}}, {"name": "variant_one", "using": {"manual_configuration": {"column_mapping": {"variant_one_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}, {"name": "variant_two", "using": {"manual_configuration": {"column_mapping": {"variant_two_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "compare_index_page_top_comparisons", "schema": "site"}, "object_relationships": [{"name": "compare_index_page_static_content", "using": {"foreign_key_constraint_on": "compare_index_page_static_content_id"}}], "array_relationships": [{"name": "compare_index_page_top_comparison_cards", "using": {"foreign_key_constraint_on": {"column": "compare_index_page_top_comparisons_id", "table": {"name": "compare_index_page_top_comparison_cards", "schema": "site"}}}}]}, {"table": {"name": "comparison_health_faqs", "schema": "site"}}, {"table": {"name": "comparison_how_is_expert_consultation", "schema": "site"}}, {"table": {"name": "comparison_why_choose_expert_consultation", "schema": "site"}}, {"table": {"name": "health_insurer_claim_settlement", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}], "array_relationships": [{"name": "health_insurer_claim_settlement_types", "using": {"foreign_key_constraint_on": {"column": "health_insurer_claim_settlement_id", "table": {"name": "health_insurer_claim_settlement_type", "schema": "site"}}}}]}, {"table": {"name": "health_insurer_claim_settlement_steps", "schema": "site"}, "object_relationships": [{"name": "health_insurer_claim_settlement_type", "using": {"foreign_key_constraint_on": "health_insurer_claim_settlement_type_id"}}]}, {"table": {"name": "health_insurer_claim_settlement_type", "schema": "site"}, "object_relationships": [{"name": "health_insurer_claim_settlement", "using": {"foreign_key_constraint_on": "health_insurer_claim_settlement_id"}}], "array_relationships": [{"name": "health_insurer_claim_settlement_steps", "using": {"foreign_key_constraint_on": {"column": "health_insurer_claim_settlement_type_id", "table": {"name": "health_insurer_claim_settlement_steps", "schema": "site"}}}}]}, {"table": {"name": "health_insurer_documents", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_expert_review", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_faqs", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_insurance_types", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_network_hospital_detail", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_plans", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}, {"name": "health_product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_policy_guide", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}], "array_relationships": [{"name": "health_insurer_policy_guide_points", "using": {"foreign_key_constraint_on": {"column": "health_insurer_policy_guide_id", "table": {"name": "health_insurer_policy_guide_points", "schema": "site"}}}}]}, {"table": {"name": "health_insurer_policy_guide_points", "schema": "site"}, "object_relationships": [{"name": "health_insurer_policy_guide", "using": {"foreign_key_constraint_on": "health_insurer_policy_guide_id"}}]}, {"table": {"name": "health_insurer_pros_cons", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_ratings", "schema": "site"}}, {"table": {"name": "health_insurer_renewal_steps", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}], "array_relationships": [{"name": "health_insurer_renewal_types", "using": {"foreign_key_constraint_on": {"column": "health_insurer_renewal_steps_id", "table": {"name": "health_insurer_renewal_type", "schema": "site"}}}}]}, {"table": {"name": "health_insurer_renewal_type", "schema": "site"}, "object_relationships": [{"name": "health_insurer_renewal_step", "using": {"foreign_key_constraint_on": "health_insurer_renewal_steps_id"}}], "array_relationships": [{"name": "health_insurer_renewal_type_steps", "using": {"foreign_key_constraint_on": {"column": "health_insurer_renewal_type_id", "table": {"name": "health_insurer_renewal_type_steps", "schema": "site"}}}}]}, {"table": {"name": "health_insurer_renewal_type_steps", "schema": "site"}, "object_relationships": [{"name": "health_insurer_renewal_type", "using": {"foreign_key_constraint_on": "health_insurer_renewal_type_id"}}]}, {"table": {"name": "health_insurer_seo", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_static_content", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_statistics", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_insurer_testimonials", "schema": "site"}, "object_relationships": [{"name": "health_insurer", "using": {"manual_configuration": {"column_mapping": {"health_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "health"}}}}]}, {"table": {"name": "health_variant_faqs", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_features", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_highlighted_features", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_policy_docs", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_ratings", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_related_variants", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}, {"name": "related_variant", "using": {"manual_configuration": {"column_mapping": {"related_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_seo", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_static_content", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "health_variant_whyoneassures", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"health_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "health"}}}}]}, {"table": {"name": "oa_doc", "schema": "site"}}, {"table": {"name": "oa_doc_file", "schema": "site"}}, {"table": {"name": "term_insurer_claim_settlement", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}], "array_relationships": [{"name": "term_insurer_claim_settlement_types", "using": {"foreign_key_constraint_on": {"column": "term_insurer_claim_settlement_id", "table": {"name": "term_insurer_claim_settlement_type", "schema": "site"}}}}]}, {"table": {"name": "term_insurer_claim_settlement_steps", "schema": "site"}, "object_relationships": [{"name": "term_insurer_claim_settlement_type", "using": {"foreign_key_constraint_on": "term_insurer_claim_settlement_type_id"}}]}, {"table": {"name": "term_insurer_claim_settlement_type", "schema": "site"}, "object_relationships": [{"name": "term_insurer_claim_settlement", "using": {"foreign_key_constraint_on": "term_insurer_claim_settlement_id"}}], "array_relationships": [{"name": "term_insurer_claim_settlement_steps", "using": {"foreign_key_constraint_on": {"column": "term_insurer_claim_settlement_type_id", "table": {"name": "term_insurer_claim_settlement_steps", "schema": "site"}}}}]}, {"table": {"name": "term_insurer_faqs", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_insurer_policy_guide", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}], "array_relationships": [{"name": "term_insurer_policy_guide_points", "using": {"foreign_key_constraint_on": {"column": "term_insurer_policy_guide_id", "table": {"name": "term_insurer_policy_guide_points", "schema": "site"}}}}]}, {"table": {"name": "term_insurer_policy_guide_points", "schema": "site"}, "object_relationships": [{"name": "term_insurer_policy_guide", "using": {"foreign_key_constraint_on": "term_insurer_policy_guide_id"}}]}, {"table": {"name": "term_insurer_pros_cons", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_insurer_ratings", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_insurer_renewal_steps", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}], "array_relationships": [{"name": "term_insurer_renewal_types", "using": {"foreign_key_constraint_on": {"column": "term_insurer_renewal_steps_id", "table": {"name": "term_insurer_renewal_type", "schema": "site"}}}}]}, {"table": {"name": "term_insurer_renewal_type", "schema": "site"}, "object_relationships": [{"name": "term_insurer_renewal_step", "using": {"foreign_key_constraint_on": "term_insurer_renewal_steps_id"}}], "array_relationships": [{"name": "term_insurer_renewal_type_steps", "using": {"foreign_key_constraint_on": {"column": "term_insurer_renewal_type_id", "table": {"name": "term_insurer_renewal_type_steps", "schema": "site"}}}}]}, {"table": {"name": "term_insurer_renewal_type_steps", "schema": "site"}, "object_relationships": [{"name": "term_insurer_renewal_type", "using": {"foreign_key_constraint_on": "term_insurer_renewal_type_id"}}]}, {"table": {"name": "term_insurer_seo", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_insurer_static_content", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_insurer_statistics", "schema": "site"}, "object_relationships": [{"name": "term_insurer", "using": {"manual_configuration": {"column_mapping": {"term_insurer_id": "id"}, "insertion_order": null, "remote_table": {"name": "insurers", "schema": "term"}}}}]}, {"table": {"name": "term_variant_addons", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_eligibility", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_faqs", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_features", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_notes", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_policy_docs", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_related_variants", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_seo", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_static_content", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "term_variant_why_one_assure", "schema": "site"}, "object_relationships": [{"name": "product_variant", "using": {"manual_configuration": {"column_mapping": {"term_variant_id": "id"}, "insertion_order": null, "remote_table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "compare_feature", "schema": "term"}, "object_relationships": [{"name": "compare_section", "using": {"foreign_key_constraint_on": "section_id"}}], "array_relationships": [{"name": "feature_values", "using": {"foreign_key_constraint_on": {"column": "feature_id", "table": {"name": "feature_value", "schema": "term"}}}}, {"name": "feature_variant_scores", "using": {"foreign_key_constraint_on": {"column": "feature_id", "table": {"name": "feature_variant_score", "schema": "term"}}}}]}, {"table": {"name": "compare_section", "schema": "term"}, "array_relationships": [{"name": "compare_features", "using": {"foreign_key_constraint_on": {"column": "section_id", "table": {"name": "compare_feature", "schema": "term"}}}}]}, {"table": {"name": "education", "schema": "term"}}, {"table": {"name": "feature_value", "schema": "term"}, "object_relationships": [{"name": "compare_feature", "using": {"foreign_key_constraint_on": "feature_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}, {"table": {"name": "feature_variant_score", "schema": "term"}, "object_relationships": [{"name": "compare_feature", "using": {"foreign_key_constraint_on": "feature_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}, {"table": {"name": "filters", "schema": "term"}, "array_relationships": [{"name": "variant_filters", "using": {"foreign_key_constraint_on": {"column": "filter_id", "table": {"name": "variant_filters", "schema": "term"}}}}]}, {"table": {"name": "insurers", "schema": "term"}, "object_relationships": [{"name": "term_insurer_seo", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_seo", "schema": "site"}}}}, {"name": "term_insurer_static_content", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_static_content", "schema": "site"}}}}], "array_relationships": [{"name": "products", "using": {"foreign_key_constraint_on": {"column": "insurer_id", "table": {"name": "products", "schema": "term"}}}}, {"name": "term_insurer_claim_settlements", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_claim_settlement", "schema": "site"}}}}, {"name": "term_insurer_faqs", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_faqs", "schema": "site"}}}}, {"name": "term_insurer_policy_guides", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_policy_guide", "schema": "site"}}}}, {"name": "term_insurer_pros_cons", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_pros_cons", "schema": "site"}}}}, {"name": "term_insurer_ratings", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_ratings", "schema": "site"}}}}, {"name": "term_insurer_renewal_steps", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_renewal_steps", "schema": "site"}}}}, {"name": "term_insurer_statistics", "using": {"manual_configuration": {"column_mapping": {"id": "term_insurer_id"}, "insertion_order": null, "remote_table": {"name": "term_insurer_statistics", "schema": "site"}}}}]}, {"table": {"name": "occupation", "schema": "term"}}, {"table": {"name": "product_riders", "schema": "term"}, "object_relationships": [{"name": "product", "using": {"foreign_key_constraint_on": "product_id"}}], "array_relationships": [{"name": "variantRidersByRiderId", "using": {"foreign_key_constraint_on": {"column": "rider_id", "table": {"name": "variant_riders", "schema": "term"}}}}, {"name": "variant_riders", "using": {"foreign_key_constraint_on": {"column": "dependent_rider_id", "table": {"name": "variant_riders", "schema": "term"}}}}]}, {"table": {"name": "product_variants", "schema": "term"}, "object_relationships": [{"name": "product", "using": {"foreign_key_constraint_on": "product_id"}}, {"name": "term_variant_seo", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_seo", "schema": "site"}}}}, {"name": "term_variant_static_content", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_static_content", "schema": "site"}}}}], "array_relationships": [{"name": "feature_values", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "feature_value", "schema": "term"}}}}, {"name": "feature_variant_scores", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "feature_variant_score", "schema": "term"}}}}, {"name": "variant_filters", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "variant_filters", "schema": "term"}}}}, {"name": "variant_riders", "using": {"foreign_key_constraint_on": {"column": "variant_id", "table": {"name": "variant_riders", "schema": "term"}}}}, {"name": "term_variant_why_one_assures", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_why_one_assure", "schema": "site"}}}}, {"name": "term_variant_features", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_features", "schema": "site"}}}}, {"name": "term_variant_policy_docs", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_policy_docs", "schema": "site"}}}}, {"name": "term_variant_related_variants", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_related_variants", "schema": "site"}}}}, {"name": "term_variant_faqs", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_faqs", "schema": "site"}}}}, {"name": "term_variant_addons", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_addons", "schema": "site"}}}}, {"name": "term_variant_eligibility", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_eligibility", "schema": "site"}}}}, {"name": "term_variant_notes", "using": {"manual_configuration": {"column_mapping": {"id": "term_variant_id"}, "insertion_order": null, "remote_table": {"name": "term_variant_notes", "schema": "site"}}}}]}, {"table": {"name": "products", "schema": "term"}, "object_relationships": [{"name": "insurer", "using": {"foreign_key_constraint_on": "insurer_id"}}], "array_relationships": [{"name": "product_riders", "using": {"foreign_key_constraint_on": {"column": "product_id", "table": {"name": "product_riders", "schema": "term"}}}}, {"name": "product_variants", "using": {"foreign_key_constraint_on": {"column": "product_id", "table": {"name": "product_variants", "schema": "term"}}}}]}, {"table": {"name": "variant_filters", "schema": "term"}, "object_relationships": [{"name": "filter", "using": {"foreign_key_constraint_on": "filter_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}, {"table": {"name": "variant_riders", "schema": "term"}, "object_relationships": [{"name": "productRiderByRiderId", "using": {"foreign_key_constraint_on": "rider_id"}}, {"name": "product_rider", "using": {"foreign_key_constraint_on": "dependent_rider_id"}}, {"name": "product_variant", "using": {"foreign_key_constraint_on": "variant_id"}}]}], "configuration": {"connection_info": {"database_url": {"from_env": "HASURA_GRAPHQL_DATABASE_URL"}, "isolation_level": "read-committed", "pool_settings": {"connection_lifetime": 600, "idle_timeout": 180, "max_connections": 50, "retries": 1}, "use_prepared_statements": true}}}, {"name": "Prod CMS", "kind": "postgres", "tables": [{"table": {"name": "page_schemas", "schema": "cms_config"}}], "configuration": {"connection_info": {"database_url": "******************************************************************************************************/cms-backend", "isolation_level": "read-committed", "use_prepared_statements": false}}}]}}