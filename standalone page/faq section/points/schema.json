{"table": {"name": "standalone_faq_section_points", "primary_key": "id", "display_field": "question", "hasura_table_name": "site_standalone_faq_section_points"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_faq_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone FAQ Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_faq_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "question", "type": "text", "required": true, "ui_config": {"label": "Question", "placeholder": "Enter question"}, "graphql_type": "String!", "default_value": ""}, {"name": "answer", "type": "text", "required": true, "ui_config": {"label": "Answer", "widget": "rich_text", "placeholder": "Enter answer"}, "graphql_type": "String!", "default_value": ""}]}