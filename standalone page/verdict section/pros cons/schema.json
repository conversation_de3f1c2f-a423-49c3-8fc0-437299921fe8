{"table": {"name": "standalone_verdict_section_pros_cons", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_standalone_verdict_section_pros_cons"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_verdict_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Verdict Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_verdict_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "Pro", "value": "pro"}, {"label": "Con", "value": "con"}]}, "graphql_type": "String!"}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "points", "type": "text[]", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "description": "Array of strings describing points", "placeholder": "Enter array of points"}, "graphql_type": "[String!]!", "default_value": []}]}