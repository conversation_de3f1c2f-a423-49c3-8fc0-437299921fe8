{"table": {"name": "standalone_page", "primary_key": "id", "display_field": "slug", "hasura_table_name": "site_standalone_page"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "slug", "type": "text", "required": true, "ui_config": {"label": "Slug", "placeholder": "Enter slug"}, "graphql_type": "String!", "unique": true}, {"name": "pill_content", "type": "text", "required": true, "ui_config": {"label": "Pill Content", "placeholder": "Enter pill content"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_title", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_description", "type": "text", "required": true, "ui_config": {"label": "Hero Description", "widget": "rich_text", "placeholder": "Enter hero description"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_image_url", "type": "text", "required": false, "ui_config": {"widget": "file_upload", "label": "Hero Image URL", "placeholder": "https://example.com/image.png"}, "validation": {"url": true}, "graphql_type": "String"}]}