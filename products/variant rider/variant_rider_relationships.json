[{"name": "product_rider", "type": "many-to-one", "ui_config": {"display_field": "slug", "display_in_list": true}, "graphql_type": "product_rider", "source_field": "rider_id", "target_field": "id", "graphql_field": "product_rider", "target_component": "health_product_rider"}, {"name": "product_variant", "type": "many-to-one", "ui_config": {"display_field": "variant_name", "display_in_list": true}, "graphql_type": "product_variant", "source_field": "variant_id", "target_field": "id", "graphql_field": "product_variant", "target_component": "health_product_variants"}]