{"table": {"name": "variant_rider", "primary_key": "id", "display_field": "rider_id", "hasura_table_name": "health_variant_rider"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "rider_id", "type": "text", "required": true, "ui_config": {"label": "Rider", "widget": "relationship_select", "display_field": "slug"}, "foreign_key": {"hasura_table": "health_product_rider", "column": "id"}, "graphql_type": "String!"}, {"name": "variant_id", "type": "text", "required": true, "ui_config": {"label": "Product Variant", "widget": "relationship_select", "display_field": "variant_name", "hidden": true}, "foreign_key": {"hasura_table": "health_product_variants", "column": "id"}, "auto_populate": {"field": "id", "source": "parent_context"}, "graphql_type": "String!"}]}