{"table": {"name": "feature_value", "primary_key": "id", "display_field": "value", "hasura_table_name": "health_feature_value"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "feature_id", "type": "text", "required": true, "ui_config": {"label": "Compare Feature", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"column": "id", "hasura_table": "health_compare_feature"}, "graphql_type": "String!"}, {"name": "variant_id", "type": "text", "required": true, "ui_config": {"label": "Product Variant", "widget": "relationship_select", "display_field": "variant_name", "hidden": true}, "foreign_key": {"table": "health_product_variants", "column": "id"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "value", "type": "text", "required": true, "ui_config": {"label": "Feature Value", "widget": "rich_text", "grid_cols": 2, "placeholder": "e.g., 100% covered, Up to ₹5 Lakhs"}, "graphql_type": "String"}, {"name": "sub_value", "type": "text", "required": true, "ui_config": {"label": "Feature Sub Value", "widget": "rich_text", "grid_cols": 2, "placeholder": "e.g., 100% covered, Up to ₹5 Lakhs"}, "graphql_type": "String"}]}