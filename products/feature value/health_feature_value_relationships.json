[{"name": "compare_feature", "type": "many-to-one", "ui_config": {"display_field": "name", "display_in_list": true}, "graphql_type": "compare_feature", "source_field": "feature_id", "target_field": "id", "graphql_field": "compare_feature", "target_component": "health_compare_feature"}, {"name": "product_variant", "type": "many-to-one", "ui_config": {"display_field": "variant_name", "display_in_list": true}, "graphql_type": "product_variant", "source_field": "variant_id", "target_field": "id", "graphql_field": "product_variant", "target_component": "health_product_variants"}]