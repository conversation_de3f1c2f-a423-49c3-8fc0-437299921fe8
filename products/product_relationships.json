[{"name": "insurer", "type": "many-to-one", "ui_config": {"display_field": "name", "display_in_list": true, "expand_by_default": true}, "graphql_type": "insurer", "source_field": "insurer_id", "target_field": "id", "graphql_field": "insurer", "target_component": "health_insurers"}, {"name": "product_variants", "type": "one-to-many", "config": {"title": "Product Variants", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "variant_name", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[product_variants!]!", "source_field": "id", "target_field": "product_id", "graphql_field": "product_variants", "target_component": "health_product_variants"}, {"name": "product_riders", "type": "one-to-many", "config": {"title": "Product Riders", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "name", "display_in_list": true, "expand_by_default": false}, "graphql_type": "[product_riders!]!", "source_field": "id", "target_field": "product_id", "graphql_field": "product_riders", "target_component": "product_riders"}]