[{"name": "product", "type": "many-to-one", "config": {"display_field": "name", "display_in_list": true, "expand_by_default": true}, "graphql_type": "product", "source_field": "product_id", "target_field": "id", "graphql_field": "product", "target_component": "health_products"}, {"name": "feature_values", "type": "one-to-many", "config": {"title": "Feature Values", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_in_list": true, "expand_by_default": false}, "graphql_type": "[feature_values!]!", "source_field": "id", "target_field": "variant_id", "graphql_field": "feature_values", "target_component": "health_feature_value"}, {"name": "health_variant_faqs", "type": "one-to-many", "config": {"title": "FAQs", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "question", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_faqs!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_faqs", "target_component": "health_variant_faqs"}, {"name": "health_variant_whyoneassures", "type": "one-to-many", "config": {"title": "Why OneAssure", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_whyOneAssures!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_whyoneassures", "target_component": "health_variant_whyOneAssures"}, {"name": "health_variant_ratings", "type": "one-to-many", "config": {"title": "Ratings", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_ratings!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_ratings", "target_component": "health_variant_ratings"}, {"name": "health_variant_related_variants", "type": "one-to-many", "config": {"title": "Related Variants", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "related_variant.variant_name", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_related_variants!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_related_variants", "target_component": "health_variant_related_variants"}, {"name": "health_variant_policy_docs", "type": "one-to-many", "config": {"title": "Policy Documents", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "label", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_policy_docs!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_policy_docs", "target_component": "health_variant_policy_docs"}, {"name": "health_variant_highlighted_features", "type": "one-to-many", "config": {"title": "Highlighted Features", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[health_variant_highlighted_features!]!", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_highlighted_features", "target_component": "health_variant_highlighted_features"}, {"name": "health_variant_static_content", "type": "one-to-one", "config": {"title": "Static Content", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "subtitle", "display_in_list": false, "expand_by_default": false}, "graphql_type": "health_variant_static_content", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_static_content", "target_component": "site_health_variant_static_content"}, {"name": "variant_riders", "type": "one-to-many", "config": {"title": "Riders", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "product_rider.name", "display_in_list": false, "expand_by_default": false}, "graphql_type": "[variant_riders!]!", "source_field": "id", "target_field": "variant_id", "graphql_field": "variant_riders", "target_component": "variant_rider"}, {"name": "health_variant_seo", "type": "one-to-one", "config": {"title": "SEO", "allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title", "display_in_list": false, "expand_by_default": false}, "graphql_type": "health_variant_seo", "source_field": "id", "target_field": "health_variant_id", "graphql_field": "health_variant_seo", "target_component": "health_variant_seo"}]