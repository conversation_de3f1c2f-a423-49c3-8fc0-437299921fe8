{"table": {"name": "product_variants", "primary_key": "id", "display_field": "variant_name", "hasura_table_name": "health_product_variants"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"label": "Variant ID", "placeholder": "Variant ID"}, "max_length": 50, "primary_key": true, "graphql_type": "String"}, {"name": "variant_name", "type": "text", "required": true, "ui_config": {"label": "Variant Name", "placeholder": "Enter variant name"}, "max_length": 255, "graphql_type": "String", "is_option_title": true}, {"name": "variant_slug", "type": "text", "unique": true, "required": true, "ui_config": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "variant-url-slug"}, "max_length": 255, "graphql_type": "String"}, {"name": "product_id", "type": "text", "required": true, "ui_config": {"label": "Product", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"table": "products", "column": "id"}, "graphql_type": "String!"}, {"name": "active", "type": "boolean", "required": true, "ui_config": {"label": "Active", "widget": "checkbox", "hidden": true}, "graphql_type": "Boolean", "default": false}, {"name": "recommendation_rule", "type": "text", "required": true, "ui_config": {"label": "Recommendation Rule", "widget": "textarea", "placeholder": "Enter recommendation rule"}, "graphql_type": "String", "default": "rate_card"}, {"name": "variant_static_content", "type": "text", "required": true, "ui_config": {"label": "Variant Static Content", "widget": "textarea", "placeholder": "Enter variant static content"}, "graphql_type": "String", "default": ""}]}