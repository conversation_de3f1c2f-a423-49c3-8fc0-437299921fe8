{"table": {"name": "products", "primary_key": "id", "display_field": "name", "hasura_table_name": "health_products"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Product Name", "placeholder": "Enter product name"}, "max_length": 255, "graphql_type": "String", "is_option_title": true}, {"name": "policy_brochure_url", "type": "text", "required": false, "ui_config": {"label": "Policy Brochure URL", "placeholder": "https://example.com/brochure.pdf"}, "validation": {"url": true}, "graphql_type": "String"}, {"name": "policy_wording_url", "type": "text", "required": false, "ui_config": {"label": "Policy Wording URL", "placeholder": "https://example.com/wording.pdf"}, "validation": {"url": true}, "graphql_type": "String"}, {"name": "insurer_id", "type": "text", "required": true, "ui_config": {"label": "Insurer", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"table": "insurers", "column": "id"}, "graphql_type": "String!"}, {"name": "active", "type": "boolean", "required": true, "ui_config": {"label": "Active", "widget": "checkbox", "hidden": true}, "graphql_type": "Boolean", "default": false}, {"name": "online", "type": "boolean", "required": true, "ui_config": {"label": "Online", "widget": "checkbox", "hidden": true}, "graphql_type": "Boolean", "default": true}, {"name": "product_metadata", "type": "jsonb", "required": true, "ui_config": {"label": "Product Metadata", "widget": "json_editor"}, "graphql_type": "jsonb", "default": {}}, {"name": "product_static_content", "type": "jsonb", "required": true, "ui_config": {"label": "Product Static Content", "widget": "json_editor"}, "graphql_type": "jsonb", "default": {}}, {"name": "plan_type", "type": "text", "required": true, "ui_config": {"label": "Plan Type", "widget": "select", "options": [{"label": "Base", "value": "base"}, {"label": "Super Top Up", "value": "super_top_up"}]}, "graphql_type": "String!", "default": "base"}, {"name": "new_business_workflow_name", "type": "text", "required": true, "ui_config": {"label": "New Business Workflow Name", "placeholder": "Enter new business workflow name"}, "graphql_type": "String!", "default": "default-sales-workflow"}, {"name": "renewal_workflow_name", "type": "text", "required": true, "ui_config": {"label": "Renewal Workflow Name", "placeholder": "Enter renewal workflow name"}, "graphql_type": "String!", "default": "test"}, {"name": "porting_workflow_name", "type": "text", "required": true, "ui_config": {"label": "Porting Workflow Name", "placeholder": "Enter porting workflow name"}, "graphql_type": "String!", "default": "test"}, {"name": "members_allowed", "type": "text[]", "required": true, "ui_config": {"label": "Members Allowed", "widget": "text_array", "description": "Array of strings describing members allowed", "placeholder": "Enter array of members allowed"}, "graphql_type": "[String!]!", "default": ["self"]}, {"name": "adult_min_age", "type": "integer", "required": true, "ui_config": {"label": "Adult Min Age", "placeholder": "Enter min age"}, "graphql_type": "Int!", "default": 18}, {"name": "adult_max_age", "type": "integer", "required": true, "ui_config": {"label": "Adult Max Age", "placeholder": "Enter max age"}, "graphql_type": "Int!", "default": 65}, {"name": "child_min_age", "type": "integer", "required": true, "ui_config": {"label": "Child Min Age", "placeholder": "Enter min age"}, "graphql_type": "Int!", "default": 0}, {"name": "child_max_age", "type": "integer", "required": true, "ui_config": {"label": "Child Max Age", "placeholder": "Enter max age"}, "graphql_type": "Int!", "default": 17}, {"name": "floater_max_children_count", "type": "integer", "required": true, "ui_config": {"label": "Floater Max Children Count", "placeholder": "Enter max children count"}, "graphql_type": "Int!", "default": 0}, {"name": "floater_max_adult_count", "type": "integer", "required": true, "ui_config": {"label": "Floater Max Adult Count", "placeholder": "Enter max adult count"}, "graphql_type": "Int!", "default": 0}, {"name": "floater_members_allowed", "type": "text[]", "required": true, "ui_config": {"label": "Floater Members Allowed", "widget": "text_array", "description": "Array of strings describing members allowed", "placeholder": "Enter array of members allowed"}, "graphql_type": "[String!]!", "default": ["self"]}]}