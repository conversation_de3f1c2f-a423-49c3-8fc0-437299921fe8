{"table": {"name": "health_variant_static_content", "primary_key": "id", "display_field": "subtitle", "hasura_table_name": "site_health_variant_static_content"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"label": "Variant Static Content ID", "placeholder": "Variant Static Content ID"}, "max_length": 16, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "health_variant_id", "type": "text", "unique": true, "required": true, "ui_config": {"label": "Health Variant", "widget": "relationship_select", "display_field": "variant_name"}, "max_length": 16, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "subtitle", "type": "text", "required": true, "ui_config": {"label": "Subtitle", "placeholder": "Enter subtitle for the health variant"}, "graphql_type": "String!", "default_value": ""}, {"name": "comparison_enabled", "type": "boolean", "required": true, "ui_config": {"label": "Comparison Enabled", "widget": "checkbox"}, "graphql_type": "Boolean!", "default_value": false}, {"name": "specialty", "type": "text", "required": true, "ui_config": {"label": "Specialty", "placeholder": "Enter specialty information"}, "graphql_type": "String!", "default_value": ""}, {"name": "best_for", "type": "text[]", "required": true, "ui_config": {"label": "Best For", "widget": "text_array", "description": "Array of strings describing what this variant is best for", "placeholder": "Enter array of best for items"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "decision_guide", "type": "text[]", "required": true, "ui_config": {"label": "Decision Guide", "widget": "text_array", "description": "Array of strings providing decision guidance", "placeholder": "Enter array of decision guide items"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "exclusions", "type": "text[]", "required": true, "ui_config": {"label": "Exclusions", "widget": "rich_text", "description": "Array of strings describing exclusions", "placeholder": "Enter array of exclusions"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "about_the_plan", "type": "text", "required": true, "ui_config": {"label": "About the Plan", "widget": "rich_text", "placeholder": "Enter about the plan content"}, "graphql_type": "String!", "default_value": ""}, {"name": "hero_title", "type": "text", "required": true, "ui_config": {"label": "Hero Title", "placeholder": "Enter hero title"}, "graphql_type": "String!", "default_value": ""}, {"name": "verdict", "type": "text", "required": true, "ui_config": {"label": "Verdict", "widget": "rich_text", "placeholder": "Enter verdict content"}, "graphql_type": "String!", "default_value": ""}]}