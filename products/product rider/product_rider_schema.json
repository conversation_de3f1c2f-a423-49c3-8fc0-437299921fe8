{"table": {"name": "product_rider", "primary_key": "id", "display_field": "name", "hasura_table_name": "health_product_rider"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"label": "Rider ID", "placeholder": "Rider ID", "hidden": true}, "max_length": 16, "primary_key": true, "graphql_type": "String", "auto_generate": true}, {"name": "product_id", "type": "text", "required": true, "ui_config": {"label": "Product", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"table": "products", "column": "id"}, "graphql_type": "String", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "name", "type": "text", "required": true, "ui_config": {"label": "Rider Name", "placeholder": "Rider Name"}, "max_length": 255, "graphql_type": "String"}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Rider Description", "placeholder": "Rider Description", "widget": "rich_text"}, "graphql_type": "String"}, {"name": "type", "type": "text", "required": true, "ui_config": {"label": "type", "placeholder": "type", "hidden": true}, "max_length": 255, "graphql_type": "String", "default_value": "no_question"}, {"name": "strategy", "type": "text", "required": true, "ui_config": {"label": "strategy", "placeholder": "strategy", "hidden": true}, "max_length": 255, "graphql_type": "String", "default_value": "api"}, {"name": "enabled", "type": "text", "required": true, "ui_config": {"label": "enabled", "placeholder": "enabled", "hidden": true}, "max_length": 255, "graphql_type": "Boolean", "default_value": false}, {"name": "order", "type": "text", "required": true, "ui_config": {"label": "order", "placeholder": "order", "hidden": true}, "max_length": 255, "graphql_type": "Integer", "default_value": "100"}]}