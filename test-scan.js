import fs from "fs";
import path from "path";

// Function to check if a directory contains the required JSON files
function hasRequiredFiles(dirPath) {
  const requiredFiles = ['schema.json', 'relationships.json', 'ui.json'];
  return requiredFiles.every(file => {
    const filePath = path.join(dirPath, file);
    return fs.existsSync(filePath);
  });
}

// Function to read and parse JSON file safely
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to read/parse ${filePath}: ${error.message}`);
    return null;
  }
}

// Function to determine if a directory is a parent (page) or child (component)
function determineType(dirPath, rootPath) {
  const relativePath = path.relative(rootPath, dirPath);
  const pathParts = relativePath.split(path.sep);
  
  // If it's directly under root, it's a page
  // If it's nested deeper, it's a component
  return pathParts.length === 1 ? 'page' : 'component';
}

// Function to recursively find directories with required JSON files
function findSchemaDirectories(rootPath, currentPath = rootPath) {
  const directories = [];
  
  try {
    const items = fs.readdirSync(currentPath, { withFileTypes: true });
    
    // Check if current directory has required files
    if (hasRequiredFiles(currentPath)) {
      directories.push(currentPath);
    }
    
    // Recursively check subdirectories
    for (const item of items) {
      if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
        const subDirPath = path.join(currentPath, item.name);
        directories.push(...findSchemaDirectories(rootPath, subDirPath));
      }
    }
  } catch (error) {
    console.error(`Failed to read directory ${currentPath}: ${error.message}`);
  }
  
  return directories;
}

// Main function to scan and display what would be processed
function scanDirectories() {
  const rootPath = process.cwd();
  console.log(`🔍 Scanning from root path: ${rootPath}\n`);
  
  const schemaDirectories = findSchemaDirectories(rootPath);
  
  console.log(`📁 Found ${schemaDirectories.length} directories with schema files:\n`);
  
  const pages = [];
  const components = [];
  
  for (const dirPath of schemaDirectories) {
    const relativePath = path.relative(rootPath, dirPath);
    const type = determineType(dirPath, rootPath);
    
    // Read schema to get name
    const schemaPath = path.join(dirPath, 'schema.json');
    const schemaJson = readJsonFile(schemaPath);
    const name = schemaJson?.table?.name || path.basename(dirPath);
    
    const item = {
      path: relativePath,
      name: name,
      type: type
    };
    
    if (type === 'page') {
      pages.push(item);
    } else {
      components.push(item);
    }
  }
  
  console.log(`📄 PAGES (${pages.length}):`);
  pages.forEach(item => {
    console.log(`  ✓ ${item.name} (${item.path})`);
  });
  
  console.log(`\n🧩 COMPONENTS (${components.length}):`);
  components.forEach(item => {
    console.log(`  ✓ ${item.name} (${item.path})`);
  });
  
  console.log(`\n📊 SUMMARY:`);
  console.log(`  Total directories: ${schemaDirectories.length}`);
  console.log(`  Pages: ${pages.length}`);
  console.log(`  Components: ${components.length}`);
}

// Run the scan
scanDirectories();
