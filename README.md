# CMS Schema Migration Script

This script recursively scans directories for JSON schema files and populates a PostgreSQL database table with the schema information.

## Features

- **Recursive Directory Scanning**: Finds all directories containing `schema.json`, `relationships.json`, and `ui.json` files
- **Automatic Type Detection**: Determines if a schema is a "page" (parent folder) or "component" (child folder)
- **Database Integration**: Creates and populates the `cms_config.page_schemas` table
- **Error Handling**: Comprehensive logging and error handling
- **Upsert Operations**: Updates existing records or inserts new ones

## Database Schema

The script creates a table `cms_config.page_schemas` with the following structure:

```sql
CREATE TABLE cms_config.page_schemas (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('page', 'component')),
  version TEXT DEFAULT '1.0.0',
  description TEXT,
  schema_definition JSONB NOT NULL,
  relationships JSONB,
  ui_schema JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);
```

## Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Configure Environment**:
   Copy `.env.example` to `.env` and fill in your database credentials:
   ```bash
   cp .env.example .env
   ```

3. **Edit .env file**:
   ```env
   DB_USER=your_db_user
   DB_HOST=your_db_host
   DB_NAME=your_db_name
   DB_PASSWORD=your_db_password
   DB_PORT=5432
   DB_SSL=false
   ```

## Usage

### Test database connection

First, test your database connection:

```bash
npm run test-db
```

### Preview what will be processed

Before running the migration, you can scan and preview what directories will be processed:

```bash
npm run scan
```

### Run the migration

Run the migration script:

```bash
npm run migrate
```

Or directly:

```bash
node script.js
```

## Directory Structure Expected

The script expects directories to contain these three files:
- `schema.json` - Contains the table schema definition
- `relationships.json` - Contains relationship definitions
- `ui.json` - Contains UI configuration

Example structure:
```
/
├── blogs/
│   ├── schema.json
│   ├── relationships.json
│   ├── ui.json
│   └── authors/
│       ├── schema.json
│       ├── relationships.json
│       └── ui.json
├── products/
│   ├── product_schema.json (will be skipped - wrong name)
│   └── faq/
│       ├── schema.json
│       ├── relationships.json
│       └── ui.json
```

## Type Classification

- **Page**: Directories directly under the root folder
- **Component**: Nested directories (subdirectories of pages)

## Logging

The script creates detailed logs in the `logs/` directory with timestamps. Logs include:
- Processing progress
- Success/error messages
- Database operations
- File reading operations

## Error Handling

- Continues processing even if individual directories fail
- Logs all errors for review
- Provides summary of processed vs. failed items
- Gracefully handles missing or malformed JSON files

## Notes

- The script uses upsert operations, so it's safe to run multiple times
- Only directories with all three required JSON files are processed
- Hidden directories and `node_modules` are automatically skipped
- The `name` field is extracted from `schema.json` table.name property
