{"table": {"name": "standalone_plans_section_plans", "primary_key": "id", "display_field": "health_variant_id", "hasura_table_name": "site_standalone_plans_section_plans"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_plans_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Plans Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_plans_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "health_variant_id", "type": "text", "required": true, "ui_config": {"label": "Health Variant", "widget": "relationship_select", "display_field": "variant_name"}, "foreign_key": {"column": "id", "hasura_table": "health_product_variants"}, "graphql_type": "String!"}]}