{"table": {"name": "standalone_claim_settlement_type", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_standalone_claim_settlement_type"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_claim_settlement_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Claim Settlement Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_claim_settlement_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "Cashless Claim", "value": "cashless claim"}, {"label": "Reimbursement Claim", "value": "reimbursement claim"}]}, "graphql_type": "String!"}]}