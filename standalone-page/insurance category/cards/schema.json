{"table": {"name": "standalone_insurance_category_section_cards", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_standalone_insurance_category_section_cards"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_insurance_category_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Insurance Category Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_insurance_category_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "points", "type": "text[]", "required": true, "ui_config": {"label": "Points", "widget": "text_array", "description": "Array of strings describing points", "placeholder": "Enter array of points"}, "graphql_type": "[String!]!", "default_value": []}, {"name": "most_popular", "type": "boolean", "required": true, "ui_config": {"label": "Most Popular", "widget": "checkbox", "hidden": true}, "graphql_type": "Boolean!", "default_value": false}, {"name": "icon_url", "type": "text", "required": false, "ui_config": {"widget": "file_upload", "label": "Icon URL", "placeholder": "https://example.com/image.png"}, "validation": {"url": true}, "graphql_type": "String"}]}