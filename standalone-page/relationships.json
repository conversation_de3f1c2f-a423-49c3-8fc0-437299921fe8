[{"name": "standalone_benefits_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_benefits_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_benefits_section", "target_component": "standalone_benefits_section"}, {"name": "standalone_faq_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_faq_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_faq_section", "target_component": "standalone_faq_section"}, {"name": "standalone_insurance_category_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_insurance_category_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_insurance_category_section", "target_component": "standalone_insurance_category_section"}, {"name": "standalone_inclusion_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_inclusion_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_inclusion_section", "target_component": "standalone_inclusion_section"}, {"name": "standalone_plans_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_plans_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_plans_section", "target_component": "standalone_plans_section"}, {"name": "standalone_what_to_look_for_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_what_to_look_for_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_what_to_look_for_section", "target_component": "standalone_what_to_look_for_section"}, {"name": "standalone_why_plans_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_why_plans_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_why_plans_section", "target_component": "standalone_why_plans_section"}, {"name": "standalone_verdict_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_verdict_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_verdict_section", "target_component": "standalone_verdict_section"}, {"name": "standalone_testimonial_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_testimonial_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_testimonial_section", "target_component": "standalone_testimonial_section"}, {"name": "standalone_seo", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "meta_title"}, "graphql_type": "standalone_seo", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_seo", "target_component": "standalone_seo"}, {"name": "standalone_renewal_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_renewal_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_renewal_section", "target_component": "standalone_renewal_section"}, {"name": "standalone_claim_settlement_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_claim_settlement_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_claim_settlement_section", "target_component": "standalone_claim_settlement_section"}, {"name": "standalone_documents_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_documents_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_documents_section", "target_component": "standalone_documents_section"}, {"name": "standalone_tax_advantage_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_tax_advantage_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_tax_advantage_section", "target_component": "standalone_tax_advantage_section"}, {"name": "standalone_key_factors_section", "type": "one-to-one", "config": {"allow_edit": true, "allow_create": true, "allow_delete": true, "display_field": "section_title"}, "graphql_type": "standalone_key_factors_section", "source_field": "id", "target_field": "standalone_page_id", "graphql_field": "standalone_key_factors_section", "target_component": "standalone_key_factors_section"}]