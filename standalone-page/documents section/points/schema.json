{"table": {"name": "standalone_documents_section_points", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_standalone_documents_section_points"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_documents_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Documents Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_documents_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "description", "type": "text", "required": true, "ui_config": {"label": "Description", "widget": "rich_text", "placeholder": "Enter description"}, "graphql_type": "String!", "default_value": ""}, {"name": "icon_url", "type": "text", "required": false, "ui_config": {"widget": "file_upload", "label": "Icon URL", "placeholder": "https://example.com/image.png"}, "validation": {"url": true}, "graphql_type": "String"}]}