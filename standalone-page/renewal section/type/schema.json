{"table": {"name": "standalone_renewal_type", "primary_key": "id", "display_field": "title", "hasura_table_name": "site_standalone_renewal_type"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_renewal_section_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Renewal Section", "widget": "relationship_select", "display_field": "section_title", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_renewal_section"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "title", "type": "text", "required": true, "ui_config": {"label": "Title", "placeholder": "Enter title"}, "graphql_type": "String!", "default_value": ""}, {"name": "type", "type": "enum", "required": true, "ui_config": {"label": "Type", "widget": "select", "options": [{"label": "Online Renewal", "value": "online_renewal"}, {"label": "Offline Renewal", "value": "offline_renewal"}]}, "graphql_type": "String!"}]}