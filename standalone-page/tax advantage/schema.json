{"table": {"name": "standalone_tax_advantage_section", "primary_key": "id", "display_field": "section_title", "hasura_table_name": "site_standalone_tax_advantage_section"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"hidden": true}, "primary_key": true, "graphql_type": "String!", "auto_generate": true}, {"name": "standalone_page_id", "type": "text", "required": true, "ui_config": {"label": "Standalone Page", "widget": "relationship_select", "display_field": "slug", "hidden": true}, "foreign_key": {"column": "id", "hasura_table": "site_standalone_page"}, "graphql_type": "String!", "auto_populate": {"field": "id", "source": "parent_context"}}, {"name": "section_title", "type": "text", "required": true, "ui_config": {"label": "Section Title", "placeholder": "Enter section title"}, "graphql_type": "String!", "default_value": ""}, {"name": "section_description", "type": "text", "ui_config": {"label": "Section Description", "widget": "rich_text", "placeholder": "Enter section description"}, "graphql_type": "String!", "default_value": ""}, {"name": "pill_content", "type": "text", "ui_config": {"label": "Pill Content", "placeholder": "Enter pill content"}, "graphql_type": "String!", "default_value": ""}]}