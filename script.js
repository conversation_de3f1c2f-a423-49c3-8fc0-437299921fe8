import { configDotenv } from "dotenv";
import axios from "axios";
import pkg from "pg";
import fs from "fs";
import path from "path";
const { Pool } = pkg;

// Initialize environment variables
configDotenv();

// Constants
const STRAPI_BASE_URL = process.env.STRAPI_BASE_URL;
const STRAPI_TOKEN = process.env.TOKEN;

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), "logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log file with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
const logFile = path.join(logsDir, `variant-data-migration-${timestamp}.txt`);

// Logging function that writes to both console and file
function logMessage(message, type = "INFO") {
  const timestampStr = new Date().toISOString();
  const logEntry = `[${timestampStr}] [${type}] ${message}`;

  // Write to console
  if (type === "ERROR") {
    console.error(message);
  } else if (type === "WARN") {
    console.warn(message);
  } else {
    console.log(message);
  }

  // Write to file
  try {
    fs.appendFileSync(logFile, logEntry + "\n", "utf8");
  } catch (error) {
    console.error(`Failed to write to log file: ${error.message}`);
  }
}

// Enhanced logging functions
const logger = {
  info: (message) => logMessage(message, "INFO"),
  warn: (message) => logMessage(message, "WARN"),
  error: (message) => logMessage(message, "ERROR"),
  success: (message) => logMessage(message, "SUCCESS"),
};

// PostgreSQL configuration
const dbConfig = {
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

// Create PostgreSQL pool
const pool = new Pool(dbConfig);

// Axios instance with common config
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_TOKEN}`,
  },
});

async function generateNewId() {
  try {
    const response = await fetch(
      "https://idgen.corp.non-prod.oneassure.in/generate",
      {
        headers: {
          "sec-ch-ua":
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          "sec-ch-ua-mobile": "?1",
          "sec-ch-ua-platform": '"Android"',
          Referer: "https://idgen.corp.non-prod.oneassure.in/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: null,
        method: "GET",
      }
    );

    if (!response.ok) {
      throw new Error(`ID generation failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    logger.error(`❌ Failed to generate new ID: ${error.message}`);
    throw error;
  }
}

// Function to check if a directory contains the required JSON files
function hasRequiredFiles(dirPath) {
  const requiredFiles = ['schema.json', 'relationships.json', 'ui.json'];
  return requiredFiles.every(file => {
    const filePath = path.join(dirPath, file);
    return fs.existsSync(filePath);
  });
}

// Function to read and parse JSON file safely
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    logger.error(`Failed to read/parse ${filePath}: ${error.message}`);
    return null;
  }
}

// Function to determine if a directory is a parent (page) or child (component)
function determineType(dirPath, rootPath) {
  const relativePath = path.relative(rootPath, dirPath);
  const pathParts = relativePath.split(path.sep);

  // If it's directly under root, it's a page
  // If it's nested deeper, it's a component
  return pathParts.length === 1 ? 'page' : 'component';
}

// Function to recursively find directories with required JSON files
function findSchemaDirectories(rootPath, currentPath = rootPath) {
  const directories = [];

  try {
    const items = fs.readdirSync(currentPath, { withFileTypes: true });

    // Check if current directory has required files
    if (hasRequiredFiles(currentPath)) {
      directories.push(currentPath);
    }

    // Recursively check subdirectories
    for (const item of items) {
      if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
        const subDirPath = path.join(currentPath, item.name);
        directories.push(...findSchemaDirectories(rootPath, subDirPath));
      }
    }
  } catch (error) {
    logger.error(`Failed to read directory ${currentPath}: ${error.message}`);
  }

  return directories;
}

// Function to create the cms_config.page_schemas table if it doesn't exist
async function createTableIfNotExists() {
  const createTableQuery = `
    CREATE SCHEMA IF NOT EXISTS cms_config;

    CREATE TABLE IF NOT EXISTS cms_config.page_schemas (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('page', 'component')),
      version TEXT DEFAULT '1.0.0',
      description TEXT,
      schema_definition JSONB NOT NULL,
      relationships JSONB,
      ui_schema JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      is_active BOOLEAN DEFAULT true
    );

    CREATE INDEX IF NOT EXISTS idx_page_schemas_name ON cms_config.page_schemas(name);
    CREATE INDEX IF NOT EXISTS idx_page_schemas_type ON cms_config.page_schemas(type);
    CREATE INDEX IF NOT EXISTS idx_page_schemas_is_active ON cms_config.page_schemas(is_active);
  `;

  try {
    await pool.query(createTableQuery);
    logger.success('✅ Table cms_config.page_schemas created/verified successfully');
  } catch (error) {
    logger.error(`❌ Failed to create table: ${error.message}`);
    throw error;
  }
}

// Function to insert or update schema data
async function insertSchemaData(schemaData) {
  const insertQuery = `
    INSERT INTO cms_config.page_schemas
    (id, name, type, version, description, schema_definition, relationships, ui_schema, created_at, updated_at, is_active)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW(), $9)
    ON CONFLICT (id)
    DO UPDATE SET
      name = EXCLUDED.name,
      type = EXCLUDED.type,
      version = EXCLUDED.version,
      description = EXCLUDED.description,
      schema_definition = EXCLUDED.schema_definition,
      relationships = EXCLUDED.relationships,
      ui_schema = EXCLUDED.ui_schema,
      updated_at = NOW(),
      is_active = EXCLUDED.is_active
    RETURNING id, name, type;
  `;

  try {
    const result = await pool.query(insertQuery, [
      schemaData.id,
      schemaData.name,
      schemaData.type,
      schemaData.version,
      schemaData.description,
      JSON.stringify(schemaData.schema_definition),
      JSON.stringify(schemaData.relationships),
      JSON.stringify(schemaData.ui_schema),
      schemaData.is_active
    ]);

    logger.success(`✅ Inserted/Updated schema: ${result.rows[0].name} (${result.rows[0].type})`);
    return result.rows[0];
  } catch (error) {
    logger.error(`❌ Failed to insert schema data for ${schemaData.name}: ${error.message}`);
    throw error;
  }
}

// Main function to process schema directories
async function processSchemaDirectories(rootPath) {
  try {
    logger.info('🔍 Starting schema directory processing...');

    // Create table if not exists
    await createTableIfNotExists();

    // Find all directories with required JSON files
    const schemaDirectories = findSchemaDirectories(rootPath);
    logger.info(`📁 Found ${schemaDirectories.length} directories with schema files`);

    let processedCount = 0;
    let errorCount = 0;

    for (const dirPath of schemaDirectories) {
      try {
        logger.info(`📂 Processing directory: ${path.relative(rootPath, dirPath)}`);

        // Read the three JSON files
        const schemaPath = path.join(dirPath, 'schema.json');
        const relationshipsPath = path.join(dirPath, 'relationships.json');
        const uiPath = path.join(dirPath, 'ui.json');

        const schemaJson = readJsonFile(schemaPath);
        const relationshipsJson = readJsonFile(relationshipsPath);
        const uiJson = readJsonFile(uiPath);

        if (!schemaJson) {
          logger.error(`❌ Failed to read schema.json from ${dirPath}`);
          errorCount++;
          continue;
        }

        // Extract name from schema.json
        const name = schemaJson.table?.name || path.basename(dirPath);
        const type = determineType(dirPath, rootPath);

        // Generate unique ID
        const id = await generateNewId();

        // Prepare schema data
        const schemaData = {
          id: id,
          name: name,
          type: type,
          version: '1.0.0',
          description: `Schema for ${name} (${type})`,
          schema_definition: schemaJson,
          relationships: relationshipsJson || [],
          ui_schema: uiJson || {},
          is_active: true
        };

        // Insert into database
        await insertSchemaData(schemaData);
        processedCount++;

      } catch (error) {
        logger.error(`❌ Error processing ${dirPath}: ${error.message}`);
        errorCount++;
      }
    }

    logger.success(`🎉 Processing completed! Processed: ${processedCount}, Errors: ${errorCount}`);

  } catch (error) {
    logger.error(`❌ Fatal error in processSchemaDirectories: ${error.message}`);
    throw error;
  }
}

// Main execution function
async function main() {
  try {
    logger.info('🚀 Starting CMS Schema Migration Script...');

    // Use current working directory as root path
    const rootPath = path.join(process.cwd(), "./standalone-page");
    logger.info(`📍 Root path: ${rootPath}`);

    await processSchemaDirectories(rootPath);

    logger.success('✅ Script completed successfully!');

  } catch (error) {
    logger.error(`❌ Script failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Close database connection
    await pool.end();
    logger.info('🔌 Database connection closed');
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}