import { configDotenv } from "dotenv";
import axios from "axios";
import pkg from "pg";
import fs from "fs";
import path from "path";
const { Pool } = pkg;

// Initialize environment variables
configDotenv();

// Constants
const STRAPI_BASE_URL = process.env.STRAPI_BASE_URL;
const STRAPI_TOKEN = process.env.TOKEN;

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), "logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log file with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
const logFile = path.join(logsDir, `variant-data-migration-${timestamp}.txt`);

// Logging function that writes to both console and file
function logMessage(message, type = "INFO") {
  const timestampStr = new Date().toISOString();
  const logEntry = `[${timestampStr}] [${type}] ${message}`;

  // Write to console
  if (type === "ERROR") {
    console.error(message);
  } else if (type === "WARN") {
    console.warn(message);
  } else {
    console.log(message);
  }

  // Write to file
  try {
    fs.appendFileSync(logFile, logEntry + "\n", "utf8");
  } catch (error) {
    console.error(`Failed to write to log file: ${error.message}`);
  }
}

// Enhanced logging functions
const logger = {
  info: (message) => logMessage(message, "INFO"),
  warn: (message) => logMessage(message, "WARN"),
  error: (message) => logMessage(message, "ERROR"),
  success: (message) => logMessage(message, "SUCCESS"),
};

// PostgreSQL configuration
const dbConfig = {
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

// Create PostgreSQL pool
const pool = new Pool(dbConfig);

// Axios instance with common config
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_TOKEN}`,
  },
});

async function generateNewId() {
  try {
    const response = await fetch(
      "https://idgen.corp.non-prod.oneassure.in/generate",
      {
        headers: {
          "sec-ch-ua":
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          "sec-ch-ua-mobile": "?1",
          "sec-ch-ua-platform": '"Android"',
          Referer: "https://idgen.corp.non-prod.oneassure.in/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: null,
        method: "GET",
      }
    );

    if (!response.ok) {
      throw new Error(`ID generation failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    logger.error(`❌ Failed to generate new ID: ${error.message}`);
    throw error;
  }
}

// recursively read files inside a directory
// add them to cms_config.page_schemas table
// each folder will have one schems.json, relationships.json, and ui.json
// add them to the table
// table will have id, name, type, version, description, schema_definition, relationships, ui_schema, created_at, updated_at, is_active
// get name from schema.json => table.name
// for parent folder, type will be page and for child folders, type will be component